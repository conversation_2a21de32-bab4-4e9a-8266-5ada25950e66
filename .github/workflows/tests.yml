name: Tests

on:
  workflow_dispatch:
  push:
    branches: [ main ]
    paths:
      - tests/*
      - pyorc/*
      - pyproject.toml
  pull_request:
    branches: [ main ]
    paths:
      - tests/*
      - pyorc/*
      - pyproject.toml
jobs:
  Test-matrix:
    name: ${{ matrix.os }} - py${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        shell: bash -l {0}
    strategy:
      fail-fast: false
      matrix:
        os: ["ubuntu-latest" ] #, "macos-latest", "windows-latest"]
        python-version: ["3.10"] # fix tests to support older versions

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-python@v4
        id: pysetup
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
          cache-dependency-path: pyproject.toml

      - name: Cache hit
        run: echo '${{ steps.pysetup.outputs.cache-hit }}'

      - name: OpenCV dependencies
        run: |
          sudo apt update
          sudo apt install libegl1 libopengl0 ffmpeg -y

      # build environment with pip
      - name: Install pyorc
        run: |
          pip install --upgrade pip
          pip install .[test,extra]

      # run all tests
      - name: Test
        run: python -m pytest --verbose --cov=pyorc --cov-report xml
      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
#      - name: Upload coverage to Codecov
#        uses: codecov/codecov-action@v5
#        env:
#          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
