sonar.projectKey=localdevices_pyorc
sonar.organization=localdevices
sonar.tests = tests
sonar.python.version = 3.9, 3.10, 3.11, 3.12
sonar.sources = pyorc,docs,examples
sonar.coverage.exclusions = docs/**, examples/**
sonar.python.coverage.reportPaths = coverage.xml

# This is the name and version displayed in the SonarCloud UI.
#sonar.projectName=pyorc
#sonar.projectVersion=1.0


# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
#sonar.sources=.

# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
