import xarray as xr
import pyorc
import cartopy
import cartopy.crs as ccrs
import cartopy.io.img_tiles as cimgt
import matplotlib.pyplot as plt
import requests
from urllib.parse import urlencode
import numpy as np
from PIL import Image
import io

# 额外的调试和测试功能

def test_baidu_tiles():
    """测试百度瓦片服务连接"""
    test_urls = [
        "https://maponline1.bdimg.com/tile/?qt=tile&x=1&y=1&z=1&styles=pl&udt=20200825",
        "https://maponline2.bdimg.com/tile/?qt=tile&x=1&y=1&z=1&styles=pl&udt=20200825"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✓ 百度瓦片服务连接正常: {url}")
                return True
        except Exception as e:
            print(f"✗ 百度瓦片服务测试失败: {url} - {e}")
            continue
    
    return False

def test_baidu_connection():
    """测试百度地图服务连接"""
    test_url = "https://api.map.baidu.com/staticimage/v2?ak=test&width=100&height=100&zoom=10"
    
    try:
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            print("✓ 百度静态图服务连接正常")
            return True
        else:
            print(f"✗ 百度静态图服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 百度静态图服务连接失败: {e}")
        return False

def test_baidu_api(api_key):
    """测试百度静态地图API（修复版本）"""
    
    # 首先测试API Key的基本有效性
    print(f"测试API Key: {api_key[:8]}...")  # 只显示前8位
    
    # 测试不同的参数组合，使用正确的格式
    test_cases = [
        {
            'name': '基础测试 - 北京天安门',
            'center': '116.403874,39.914889',
            'zoom': '10',
            'width': '400',
            'height': '400',
            'scale': '1',
            'ak': api_key
        },
        {
            'name': '高清图测试',
            'center': '116.403874,39.914889',
            'zoom': '12',
            'width': '512',
            'height': '512',
            'scale': '2',  # 高清图
            'dpiType': 'ph',  # 高分屏
            'ak': api_key
        },
        {
            'name': '带标记点测试',
            'center': '116.403874,39.914889',
            'zoom': '14',
            'width': '600',
            'height': '400',
            'scale': '2',
            'markers': '116.403874,39.914889',  # 标记点
            'markerStyles': 'l,A,0xFF0000',  # 大号红色标记A
            'ak': api_key
        }
    ]   
    
    test_url = "https://api.map.baidu.com/staticimage/v2"
    
    for i, params in enumerate(test_cases):
        print(f"\n--- {params['name']} ---")
        test_params = {k: v for k, v in params.items() if k != 'name'}
        print(f"测试参数: {test_params}")
        
        try:
            response = requests.get(test_url, params=test_params, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"内容类型: {content_type}")
                print(f"内容长度: {len(response.content)} bytes")
                
                # 检查是否为错误响应
                if 'json' in content_type.lower() or 'text' in content_type.lower():
                    try:
                        json_data = response.json()
                        print(f"❌ API返回错误: {json_data}")
                        
                        # 详细错误分析
                        status = json_data.get('status')
                        if status == 101:
                            print("🔧 解决方案: 检查API Key是否正确")
                        elif status == 102:
                            print("🔧 解决方案: 确认API Key是否有静态地图服务权限")
                        elif status == 200:
                            print("🔧 解决方案: 检查参数格式，特别是坐标格式")
                        elif status == 300:
                            print("🔧 解决方案: 检查是否超出配额限制")
                            
                        continue
                    except:
                        # 可能是文本错误信息
                        try:
                            error_text = response.text
                            print(f"❌ API返回文本错误: {error_text[:200]}")
                            continue
                        except:
                            pass
                
                # 尝试解析为图像
                try:
                    img = Image.open(io.BytesIO(response.content))
                    print(f"✅ 成功获取地图图像: {img.size}, 模式: {img.mode}")
                    
                    # 保存测试图像
                    filename = f'test_baidu_map_{i+1}.png'
                    img.save(filename)
                    print(f"📁 测试图像已保存: {filename}")
                    return True
                    
                except Exception as img_error:
                    print(f"❌ 图像解析失败: {img_error}")
                    
                    # 保存原始数据用于调试
                    debug_filename = f'debug_baidu_response_{i+1}.dat'
                    with open(debug_filename, 'wb') as f:
                        f.write(response.content)
                    print(f"🔍 原始响应已保存: {debug_filename}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_text = response.text
                    print(f"错误内容: {error_text[:200]}")
                except:
                    print("无法读取错误内容")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    return False


class BaiduStaticMap:
    def __init__(self, ak):
        """
        百度静态地图服务
        
        Parameters:
        -----------
        ak : str
            百度地图API密钥（必需）
        """
        self.ak = ak
        self.base_url = "https://api.map.baidu.com/staticimage/v2"
    
    def get_map_image(self, center_lon, center_lat, zoom=15, width=1024, height=1024, scale=1, markers=None, coordtype='bd09ll'):
        """
        获取静态地图图像（根据百度官方文档修正）
        
        Parameters:
        -----------
        center_lon : float
            中心点经度
        center_lat : float  
            中心点纬度
        zoom : int
            缩放级别 (3-18 for 高清图, 3-19 for 低清图)
        width : int
            图片宽度，最大1024（scale=2时最大512）
        height : int
            图片高度，最大1024（scale=2时最大512）
        scale : int
            普通/高清 1是普通，2是高清
        markers : list, optional
            标记点列表 [(lon1, lat1), (lon2, lat2), ...]
        coordtype : str
            坐标系类型：bd09ll(百度经纬度), gcj02ll(国测局), wgs84ll(GPS)
        """
        print(f"检查API Key长度: {len(self.ak)}")
        
        # 根据百度API文档，正确的参数格式
        params = {
            'center': f"{center_lon},{center_lat}",
            'zoom': str(zoom),  # 确保是字符串
            'width': str(width),
            'height': str(height),
            'scale': scale,  # 普通/高清，1或2
            'coordtype': coordtype,  # 坐标系类型
            'ak': self.ak
        }
        
        # 添加高清屏设置
        if scale == 2:
            params['dpiType'] = 'ph'  # 高分屏
            # 高清图时尺寸限制为512
            if width > 512:
                params['width'] = '512'
                print("高清模式下宽度调整为512")
            if height > 512:
                params['height'] = '512'
                print("高清模式下高度调整为512")
        
        # 添加标记点
        if markers and isinstance(markers, list):
            # 将坐标元组列表转换为百度格式
            marker_locations = '|'.join([f"{lon},{lat}" for lon, lat in markers])
            params['markers'] = marker_locations
            
            # 构造标记样式 - 为每个标记设置样式
            # 格式：size,label,color|size,label,color|...
            marker_styles = []
            for i, (lon, lat) in enumerate(markers):
                # 循环使用不同的标签和颜色
                labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
                colors = ['0xFF0000', '0x00FF00', '0x0000FF', '0xFFFF00', '0xFF00FF']
                
                label = labels[i % len(labels)]
                color = colors[i % len(colors)]
                marker_styles.append(f"l,{label},{color}")  # l=large size
            
            params['markerStyles'] = '|'.join(marker_styles)
        
        print(f"请求URL: {self.base_url}")
        print(f"请求参数: {params}")
        
        try:
            response = requests.get(self.base_url, params=params, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                print(f"响应内容类型: {content_type}")
                print(f"响应内容长度: {len(response.content)}")
                
                # 如果是JSON响应，可能包含错误信息
                if 'json' in content_type.lower():
                    try:
                        error_info = response.json()
                        print(f"API错误信息: {error_info}")
                        
                        # 分析常见错误
                        status = error_info.get('status')
                        if status == 101:
                            print("❌ 错误: API Key无效")
                        elif status == 102:
                            print("❌ 错误: 没有权限使用静态地图服务")
                        elif status == 200:
                            print("❌ 错误: 请求参数无效，请检查坐标格式和参数")
                        elif status == 300:
                            print("❌ 错误: 超出配额限制")
                        
                        return None
                    except:
                        pass
                
                # 检查是否为文本错误信息
                if 'text' in content_type.lower():
                    try:
                        error_text = response.text
                        print(f"API返回文本错误: {error_text}")
                        return None
                    except:
                        pass
                
                # 尝试解析为图像
                try:
                    image = Image.open(io.BytesIO(response.content))
                    print(f"✓ 成功获取地图图像: {image.size}")
                    return image
                except Exception as img_error:
                    print(f"图像解析失败: {img_error}")
                    # 保存原始响应用于调试
                    with open('debug_baidu_response.dat', 'wb') as f:
                        f.write(response.content)
                    print("原始响应已保存为 debug_baidu_response.dat")
                    return None
                    
            else:
                print(f"百度API返回HTTP错误: {response.status_code}")
                try:
                    error_text = response.text
                    print(f"错误内容: {error_text}")
                except:
                    pass
                return None
                
        except Exception as e:
            print(f"请求百度地图失败: {e}")
            return None

# 坐标转换工具（如果需要从其他坐标系转换到百度坐标系）
def convert_to_baidu_coords(lon, lat, from_coord='wgs84'):
    """
    坐标转换到百度坐标系
    
    Parameters:
    -----------
    lon, lat : float
        经纬度坐标
    from_coord : str
        原坐标系：'wgs84', 'gcj02'
        
    Returns:
    --------
    tuple : (bd_lon, bd_lat) 百度坐标系下的经纬度
    
    注意：这里提供简化的转换，实际项目中建议使用百度官方转换API
    """
    # 这里可以调用百度的坐标转换API
    # http://api.map.baidu.com/geoconv/v1/
    # 为简化示例，这里直接返回原坐标（假设已经是百度坐标）
    print(f"坐标转换: {from_coord} -> bd09ll")
    return lon, lat

# 主要使用代码
def plot_camera_config_with_baidu(cam_config, api_key=None, method="static", coordtype='bd09ll', save_path=None):
    """
    使用百度地图绘制相机配置（修复版）
    
    Parameters:
    -----------
    cam_config : pyorc.CameraConfig
        相机配置对象
    api_key : str, optional
        百度地图API密钥
    method : str
        绘图方法：'tiles'使用瓦片服务，'static'使用静态地图API，'simple'简单绘图
    coordtype : str
        坐标系类型：'bd09ll'(百度), 'gcj02ll'(国测局), 'wgs84ll'(GPS)
    """
    
    if method == "static" and api_key:
        # 方案1: 使用静态地图API
        try:
            # 从GCPs获取地理范围
            gcps = cam_config.gcps
            if 'dst' in gcps:
                lons = [coord[0] for coord in gcps['dst']]
                lats = [coord[1] for coord in gcps['dst']]
                
                center_lon = np.mean(lons)
                center_lat = np.mean(lats)
                
                # 计算合适的缩放级别
                lon_range = max(lons) - min(lons)
                lat_range = max(lats) - min(lats)
                max_range = max(lon_range, lat_range)
                
                # 根据范围估算缩放级别
                if max_range > 0.01:
                    zoom = 15
                elif max_range > 0.005:
                    zoom = 16
                elif max_range > 0.001:
                    zoom = 17
                else:
                    zoom = 18
                print(f"缩放级别: {zoom}, max_range: {max_range}")

                # 如果原始坐标不是百度坐标系，需要转换
                if coordtype != 'bd09ll':
                    print(f"原始坐标系: {coordtype}, 需要转换到百度坐标系")
                    # 这里可以添加坐标转换逻辑
                    # 为简化演示，假设输入坐标已经是目标坐标系
                
                # markers 应该是 gcps['dst'] 里的所有点
                marker_points = [(lon, lat) for lon, lat in gcps['dst']]
                
                # 创建静态地图
                baidu_map = BaiduStaticMap(api_key)
                map_image = baidu_map.get_map_image(
                    center_lon, center_lat, 
                    zoom=zoom, 
                    scale=2,  # 高清
                    width=1024,
                    height=1024,
                    markers=marker_points,
                    coordtype=coordtype
                )
                
                if map_image:
                    # 创建图形
                    fig, ax = plt.subplots(figsize=(12, 8))
                    
                    # 显示地图图像
                    ax.imshow(map_image)
                    ax.set_title("Camera Configuration - Baidu Map")
                    ax.axis('off')
                    
                    # 在图像上标注GCP点（需要转换坐标）
                    # 这里简化处理，只显示图像

                    if save_path:
                        plt.savefig(save_path, dpi=300, bbox_inches='tight', pad_inches=0.1)
                        print(f"✓ 保存成功: {save_path}")
                    
                    return ax
                else:
                    print("静态地图API失败，尝试简单绘图方案")
                    return plot_camera_config_with_baidu(cam_config, method="simple")
            else:
                print("未找到dst坐标，尝试简单绘图方案")
                return plot_camera_config_with_baidu(cam_config, method="simple")
                
        except Exception as e:
            print(f"静态地图API失败: {e}")
            return plot_camera_config_with_baidu(cam_config, method="simple")

# 使用示例
def example_usage():
    """使用示例"""
    
    # 替换为你的百度地图API Key
    BAIDU_API_KEY = "irMGPfRrhNNPx0clwYHRVbaT7SPQohup"
    
    # 测试API连接
    print("=== 测试百度地图API ===")
    if test_baidu_api(BAIDU_API_KEY):
        print("✅ 百度地图API测试成功")
    else:
        print("❌ 百度地图API测试失败")
    
    # 模拟相机配置对象（实际使用时替换为真实的cam_config）
    class MockCameraConfig:
        def __init__(self):
            # 模拟GCP数据 - 北京市区域的几个点（百度坐标系）
            self.gcps = {
                'dst': [
                    (116.403874, 39.914889),  # 天安门
                    (116.407394, 39.90923),   # 故宫
                    (116.391467, 39.906901),  # 西单
                    (116.418757, 39.917723)   # 王府井
                ]
            }
    
    # 创建模拟相机配置
    mock_cam_config = MockCameraConfig()
    
    # 绘制地图
    print("\n=== 绘制相机配置地图 ===")
    ax = plot_camera_config_with_baidu(
        mock_cam_config, 
        api_key=BAIDU_API_KEY, 
        method="static",
        coordtype='bd09ll'  # 百度坐标系
    )
    
    if ax:
        plt.tight_layout()
        plt.savefig('camera_config_baidu_map.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("✅ 地图已保存为 camera_config_baidu_map.png")
    else:
        print("❌ 地图绘制失败")

if __name__ == "__main__":
    example_usage()