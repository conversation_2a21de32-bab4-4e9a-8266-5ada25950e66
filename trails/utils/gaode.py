import xarray as xr
import pyorc
import cartopy
import cartopy.crs as ccrs
import cartopy.io.img_tiles as cimgt
import matplotlib.pyplot as plt
import requests
from urllib.parse import urlencode
import numpy as np
from PIL import Image
import io

# 额外的调试和测试功能

def test_gaode_tiles():
    """测试高德瓦片服务连接"""
    test_urls = [
        "https://webst01.is.autonavi.com/appmaptile?style=6&x=1&y=1&z=1",
        "https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x=1&y=1&z=1"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✓ 高德瓦片服务连接正常: {url}")
                return True
        except Exception as e:
            print(f"✗ 高德瓦片服务测试失败: {url} - {e}")
            continue
    
    return False

# 额外的调试和测试功能
def test_gaode_connection():
    """测试高德地图服务连接"""
    test_url = "https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=6&x=1&y=1&z=1"
    
    try:
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            print("✓ 高德瓦片服务连接正常")
            return True
        else:
            print(f"✗ 高德瓦片服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 高德瓦片服务连接失败: {e}")
        return False

def test_gaode_api(api_key):
    """测试高德静态地图API（修复版本）"""
    
    # 首先测试API Key的基本有效性
    print(f"测试API Key: {api_key[:8]}...")  # 只显示前8位
    
    # 测试不同的参数组合，使用正确的格式
    test_cases = [
        # {
        #     'name': '基础测试 - 北京天安门',
        #     'location': '116.397428,39.90923',
        #     'zoom': '10',
        #     'size': '400*400',
        #     'scale': '1',
        #     'maptype': '1',
        #     'format': 'png',
        #     'key': api_key
        # },
        # {
        #     'name': '卫星图测试',
        #     'location': '116.397428,39.90923',
        #     'zoom': '12',
        #     'size': '512*512',
        #     'scale': '1', 
        #     'maptype': '2',  # 卫星图
        #     'format': 'png',
        #     'key': api_key
        # },
        {
            'name': '带标记点测试',
            'location': '116.397428,39.90923',
            'zoom': '14',
            'size': '600*400',
            'scale': '2',
            # 'maptype': '1',
            'format': 'png',
            'markers': 'mid,0xFF0000,A:116.397428,39.90923',  # 红色标记
            'key': api_key
        }
    ]   
    
    test_url = "https://restapi.amap.com/v3/staticmap"
    
    for i, params in enumerate(test_cases):
        print(f"\n--- {params['name']} ---")
        test_params = {k: v for k, v in params.items() if k != 'name'}
        print(f"测试参数: {test_params}")
        
        try:
            response = requests.get(test_url, params=test_params, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"内容类型: {content_type}")
                print(f"内容长度: {len(response.content)} bytes")
                
                # 检查是否为错误响应
                if 'json' in content_type.lower() or len(response.content) < 1000:
                    try:
                        json_data = response.json()
                        print(f"❌ API返回错误: {json_data}")
                        
                        # 详细错误分析
                        infocode = json_data.get('infocode')
                        if infocode == '10001':
                            print("🔧 解决方案: 检查API Key是否正确，是否已过期")
                        elif infocode == '10002':
                            print("🔧 解决方案: 确认API Key是否有静态地图服务权限")
                        elif infocode == '10003':
                            print("🔧 解决方案: 检查是否超出每日配额")
                        elif infocode == '20003':
                            print("🔧 解决方案: 检查参数格式，特别是坐标格式")
                            
                        continue
                    except:
                        pass
                
                # 尝试解析为图像
                try:
                    img = Image.open(io.BytesIO(response.content))
                    print(f"✅ 成功获取地图图像: {img.size}, 模式: {img.mode}")
                    
                    # 保存测试图像
                    filename = f'test_gaode_map_{i+1}.png'
                    img.save(filename)
                    print(f"📁 测试图像已保存: {filename}")
                    return True
                    
                except Exception as img_error:
                    print(f"❌ 图像解析失败: {img_error}")
                    
                    # 保存原始数据用于调试
                    debug_filename = f'debug_response_{i+1}.dat'
                    with open(debug_filename, 'wb') as f:
                        f.write(response.content)
                    print(f"🔍 原始响应已保存: {debug_filename}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_text = response.text
                    print(f"错误内容: {error_text[:200]}")
                except:
                    print("无法读取错误内容")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    return False


class GaodeStaticMap:
    def __init__(self, key):
        """
        高德静态地图服务
        
        Parameters:
        -----------
        key : str
            高德地图API密钥（必需）
        """
        self.key = key
        self.base_url = "https://restapi.amap.com/v3/staticmap"
    
    def get_map_image(self, center_lon, center_lat, zoom=15, size="1024*1024", scale=1, markers=None):
        """
        获取静态地图图像（根据高德官方文档修正）
        
        Parameters:
        -----------
        center_lon : float
            中心点经度
        center_lat : float  
            中心点纬度
        zoom : int
            缩放级别 (3-18)
        size : str
            图片尺寸 "width*height" 最大支持1024*1024
        scale : int
            普通/高清 1是普通，2是高清
        markers : str, optional
            标记点格式: "经度,纬度:大小:颜色:标签"
        """
        # 根据高德API文档，正确的参数格式
        print(f"check api key: {len(self.key)}")
        params = {
            'location': f"{center_lon},{center_lat}",
            'zoom': str(zoom),  # 确保是字符串
            'size': size,
            'scale': scale,  # 普通/高清，1或2
            'format': 'png',  # 图片格式
            'key': self.key
        }
        
        # 添加标记点
        # 添加标记点 (修正部分)
        # markers 参数应为列表 [(lon1, lat1), (lon2, lat2), ...]
        if markers and isinstance(markers, list):
            # 将坐标元组列表转换为 "lon1,lat1;lon2,lat2;..." 的字符串格式 [[6]]
            marker_locations = ';'.join([f"{lon},{lat}" for lon, lat in markers])
            # 构造 markers 参数字符串，使用简单样式 (例如: large, 红色, 标记为 'A')
            # 样式格式: markers=样式:坐标1;坐标2;...
            # 你可以根据需要调整样式，例如 "mid,0xFF0000,A" 或参考官方文档使用默认样式
            # 这里使用一种常见的默认样式格式
            params['markers'] = f"large,0xFF0000,A:{marker_locations}" # [[1]]
            # 或者如果想使用最简单的默认样式，可以省略样式部分（如果API支持）
            # params['markers'] = marker_locations # 尝试仅坐标，具体看API要求
        
        print(f"请求URL: {self.base_url}")
        print(f"请求参数: {params}")
        
        try:
            response = requests.get(self.base_url, params=params, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                print(f"响应内容类型: {content_type}")
                print(f"响应内容长度: {len(response.content)}")
                
                # 如果是JSON响应，可能包含错误信息
                if 'json' in content_type.lower():
                    try:
                        error_info = response.json()
                        print(f"API错误信息: {error_info}")
                        
                        # 分析常见错误
                        if error_info.get('infocode') == '10001':
                            print("❌ 错误: API Key无效或已过期")
                        elif error_info.get('infocode') == '10002':
                            print("❌ 错误: 没有权限使用静态地图服务")
                        elif error_info.get('infocode') == '10003':
                            print("❌ 错误: 超出每日配额限制")
                        elif error_info.get('infocode') == '20003':
                            print("❌ 错误: 请求参数无效，请检查坐标格式和API Key")
                        
                        return None
                    except:
                        pass
                
                # 如果是图像类型
                if 'image' in content_type.lower():
                    try:
                        image = Image.open(io.BytesIO(response.content))
                        print(f"✓ 成功获取地图图像: {image.size}")
                        return image
                    except Exception as img_error:
                        print(f"图像解析失败: {img_error}")
                        return None
                else:
                    # 尝试作为图像处理（有时content-type不准确）
                    try:
                        image = Image.open(io.BytesIO(response.content))
                        print(f"✓ 成功解析图像（忽略content-type）: {image.size}")
                        return image
                    except:
                        print(f"无法解析响应为图像")
                        return None
            else:
                print(f"高德API返回HTTP错误: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"请求高德地图失败: {e}")
            return None

# 主要使用代码
def plot_camera_config_with_gaode(cam_config, api_key=None, method="tiles", save_path=None):
    """
    使用高德地图绘制相机配置（修复版）
    
    Parameters:
    -----------
    cam_config : pyorc.CameraConfig
        相机配置对象
    api_key : str, optional
        高德地图API密钥
    method : str
        绘图方法：'tiles'使用瓦片服务，'static'使用静态地图API，'simple'简单绘图
    """
    # print(f"check camera config: {cam_config}")
    
    if method == "static" and api_key:
        # 方案2: 使用静态地图API
        try:
            # 从GCPs获取地理范围
            gcps = cam_config.gcps
            if 'dst' in gcps:
                lons = [coord[0] for coord in gcps['dst']]
                lats = [coord[1] for coord in gcps['dst']]
                
                center_lon = np.mean(lons)
                center_lat = np.mean(lats)
                
                # 计算合适的缩放级别
                lon_range = max(lons) - min(lons)
                lat_range = max(lats) - min(lats)
                max_range = max(lon_range, lat_range)
                
                # 根据范围估算缩放级别
                if max_range > 0.01:
                    zoom = 14
                elif max_range > 0.005:
                    zoom = 15
                elif max_range > 0.001:
                    zoom = 16
                else:
                    zoom = 17
                print(f"缩放级别: {zoom}, max_range: {max_range}")

                # markers 应该是 gcps['dst'] 里的所有点
                marker_points = [(lon, lat) for lon, lat in gcps['dst']] # 格式: [(lon1, lat1), ...]
                
                # 创建静态地图
                gaode_map = GaodeStaticMap(api_key)
                map_image = gaode_map.get_map_image(
                    center_lon, center_lat, 
                    zoom=zoom, 
                    scale=2,
                    size="1024*1024",
                    markers=marker_points
                )
                
                if map_image:
                    # 创建图形
                    fig, ax = plt.subplots(figsize=(12, 8))
                    
                    # 显示地图图像
                    ax.imshow(map_image)
                    ax.set_title("Camera Configuration - Gaode Map")
                    ax.axis('off')
                    
                    # 在图像上标注GCP点（需要转换坐标）
                    # 这里简化处理，只显示图像

                    # 如果提供了保存路径，则保存图片
                    if save_path:
                        plt.savefig(save_path, dpi=300, bbox_inches='tight', pad_inches=0.1)
                        print(f"图片已保存到: {save_path}")
                    
                    return ax
                else:
                    print("静态地图API失败，尝试简单绘图方案")
                    return plot_camera_config_with_gaode(cam_config, method="simple")
            else:
                print("未找到dst坐标，尝试简单绘图方案")
                return plot_camera_config_with_gaode(cam_config, method="simple")
                
        except Exception as e:
            print(f"静态地图API失败: {e}")
            return plot_camera_config_with_gaode(cam_config, method="simple")
