import requests
import matplotlib
import platform

def test_network_connection():
    """测试网络连接"""
    test_urls = [
        "https://www.baidu.com",
        "https://tile.openstreetmap.org/1/1/1.png"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            print(f"✓ {url} 连接正常")
        except Exception as e:
            print(f"✗ {url} 连接失败: {e}")

# 解决中文字体显示问题
def fix_chinese_display():
    """修复中文显示问题"""
    system = platform.system()
    
    if system == "Linux":
        # Linux系统的中文字体设置
        fonts = ['WenQuanYi Micro Hei', 'SimHei', 'DejaVu Sans']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'DejaVu Sans']
    else:  # Windows  
        fonts = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    print(f"已设置字体: {fonts}")