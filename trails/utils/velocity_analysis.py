import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_velocity_data(file_path='velocity_data.csv'):
    """
    加载流速数据
    """
    return pd.read_csv(file_path)

def calculate_velocity_magnitude(df):
    """
    计算流速大小并过滤掉流速为0的数据
    """
    df['velocity_magnitude'] = np.sqrt(df['v_x']**2 + df['v_y']**2)
    
    # 过滤掉流速为0的数据
    initial_count = len(df)
    df = df[df['velocity_magnitude'] > 0].copy()
    filtered_count = len(df)
    
    print(f"过滤前数据点数: {initial_count}")
    print(f"过滤后数据点数: {filtered_count}")
    print(f"过滤掉的0流速点数: {initial_count - filtered_count}")
    
    return df

def get_top_velocity_points(df, n_points=5):
    """
    获取平均流速最大的n个点
    """
    # 计算每个点的平均流速
    point_stats = df.groupby(['x', 'y']).agg({
        'velocity_magnitude': 'mean',
        'v_x': 'mean',
        'v_y': 'mean'
    }).reset_index()
    
    # 按平均流速排序，获取前n个点
    top_points = point_stats.nlargest(n_points, 'velocity_magnitude')
    return top_points

def plot_velocity_time_series(df, points, save_path=None):
    """
    绘制选定点的流速时间序列
    """
    fig, axes = plt.subplots(2, 1, figsize=(12, 10))
    
    for _, point in points.iterrows():
        x, y = point['x'], point['y']
        
        # 筛选该点的数据
        point_data = df[(df['x'] == x) & (df['y'] == y)]
        
        # 绘制v_x时间序列
        axes[0].plot(point_data['time'], point_data['v_x'], 
                    label=f'Point ({x:.2f}, {y:.2f})', marker='o', markersize=3)
        
        # 绘制v_y时间序列
        axes[1].plot(point_data['time'], point_data['v_y'], 
                    label=f'Point ({x:.2f}, {y:.2f})', marker='o', markersize=3)
    
    axes[0].set_xlabel('Time (s)')
    axes[0].set_ylabel('v_x (m/s)')
    axes[0].set_title('v_x changes with time')
    axes[0].legend()
    axes[0].grid(True)
    
    axes[1].set_xlabel('Time (s)')
    axes[1].set_ylabel('v_y (m/s)')
    axes[1].set_title('v_y changes with time')
    axes[1].legend()
    axes[1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_velocity_distribution(df, save_path=None):
    """
    使用箱型图展示 v_x、v_y 与 速度大小 的分布
    """
    # 组装为长表，便于一次性画三类变量的箱型图
    data = pd.DataFrame({
        'variable': np.repeat(['v_x', 'v_y', 'speed'], [len(df), len(df), len(df)]),
        'value': np.concatenate([
            df['v_x'].values,
            df['v_y'].values,
            df['velocity_magnitude'].values
        ])
    })

    fig, ax = plt.subplots(figsize=(10, 6))
    sns.boxplot(data=data, x='variable', y='value', showfliers=True, ax=ax)

    ax.set_xlabel('')
    ax.set_ylabel('Velocity (m/s)')
    ax.set_title('Velocity Distribution (Boxplot)')
    ax.grid(True, axis='y', alpha=0.3)

    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def analyze_velocity_data(data_path='velocity_data.csv', n_points=5):
    """
    完整的流速分析流程（保留分布图，但改为箱型图；去除时间序列图）
    """
    print("正在加载数据...")
    df = load_velocity_data(data_path)

    print("正在计算流速大小...")
    df = calculate_velocity_magnitude(df)

    # 绘制速度分布（箱型图）
    print("正在绘制速度分布箱型图...")
    plot_velocity_distribution(df, save_path='velocity_distribution.png')

    # 按平均流速选择高流速点
    print(f"正在获取平均流速最大的{n_points}个点...")
    top_points = get_top_velocity_points(df, n_points)
    print("\n选中的高流速点:")
    print(top_points[['x', 'y', 'velocity_magnitude']])

    return df, top_points