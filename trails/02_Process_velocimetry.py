import pyorc
import matplotlib.pyplot as plt
import xarray as xr
import cartopy
import cartopy.crs as ccrs
import cartopy.io.img_tiles as cimgt
from dask.diagnostics import ProgressBar
from matplotlib import patches

cam_config = pyorc.load_camera_config("trails/yuwangling/yuwangling.json")
video_file = "trails/yuwangling/ch01_20250414145900.mp4"
# set coordinates that encapsulate the water surface at minimum. Areas outside will be used for stabilization
stabilize = [
    [70, 0], # 左上角
    [1000, 719], # 左下角
    [1279, 150], # 右下角
    [460, 0] # 右上角
]
video = pyorc.Video(
    video_file,
    camera_config=cam_config,
    start_frame=0,
    end_frame=125,
    stabilize=stabilize,
    h_a=0.,
)

corners = [
    [775, 600], # 左下角
    [75, 80], # 左上角
    [750, 70], # 右上角
    [1240, 140] # 右下角
]
cam_config.set_bbox_from_corners(corners)


# some keyword arguments for fancy polygon plotting
patch_kwargs = {
    "alpha": 0.5,
    "zorder": 2,
    "edgecolor": "w",
    "label": "Area of interest",
}

frame = video.get_frame(0, method="rgb")

# add the polygon to the axes
patch = patches.Polygon(
    stabilize,
    **patch_kwargs
)

da = video.get_frames()

da[0].frames.plot(cmap="gray")

# da_norm = da.frames.time_diff(abs=False, thres=0.)
# da_norm = da_norm.frames.minmax(min=0.)
da_norm = da.frames.normalize()
p = da_norm[0].frames.plot(cmap="gray")
plt.colorbar(p)


f = plt.figure(figsize=(16, 9))
da_norm_proj = da_norm.frames.project(method="numpy")
da_norm_proj[0].frames.plot(cmap="gray")
