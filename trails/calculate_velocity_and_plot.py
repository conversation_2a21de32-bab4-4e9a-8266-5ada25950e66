import pyorc
import matplotlib.pyplot as plt
import xarray as xr
import cartopy
import cartopy.crs as ccrs
import cartopy.io.img_tiles as cimgt
from dask.diagnostics import ProgressBar
from matplotlib import patches
from matplotlib.colors import Normalize
import numpy as np
import copy

cam_config = pyorc.load_camera_config("yuwangling/yuwangling.json") # 配置文件是统一的，都是针对育王岭
video_file = "yuwangling/0812_speed_720.mp4" # 这个视频要改，计算出不同时间的流速
# 设置包含水面最小范围的坐标。范围外的区域将用于稳定
stabilize = [
    [775, 600], # 左下角
    [75, 80], # 左上角
    [750, 70], # 右上角
    [1240, 140] # 右下角
]
# gcps = cam_config.gcps
# print(f"cam_config.gcps: {gcps}")
# z_0 = gcps['z_0']
# h_ref = gcps['h_ref']
# print(f"z_0: {z_0}, h_ref: {h_ref}")

video = pyorc.Video(
    video_file,
    camera_config=cam_config,
    start_frame=300,
    end_frame=600,
    stabilize=stabilize,
    h_a=0.,
)


# 图形化测算流速的范围
patch_kwargs = {
    "alpha": 0.5,
    "zorder": 2,
    "edgecolor": "w",
    "label": "Area of interest",
}
f, ax = plt.subplots(1, 1, figsize=(10, 6))

frame = video.get_frame(0, method="rgb")
# 在笔记本风格的窗口中绘制帧
ax.imshow(frame)
# 将多边形添加到坐标轴上
patch = patches.Polygon(
    stabilize,
    **patch_kwargs
)
p = ax.add_patch(patch)

# 只是用来画图检查投影后的坐标系准确度
da = video.get_frames()

# 画出gray处理前的图像，但是并没有转化坐标系
p = da[0].frames.plot()
plt.colorbar(p)

# 画出gray处理后的图像，已经转化了坐标系
# f = plt.figure(figsize=(16, 9))
da_proj = da.frames.project(method="numpy")
da_proj[0].frames.plot()


da_norm = da.frames.normalize()
p = da_norm[0].frames.plot(cmap="gray")
plt.colorbar(p)

f = plt.figure(figsize=(16, 9))
da_norm_proj = da_norm.frames.project(method="numpy")
da_norm_proj[0].frames.plot(cmap="gray")

# 计算流速
piv = da_norm_proj.frames.get_piv(engine="numba")

# 查看流速数据情况
ds = piv.copy()

# 正确的做法：先计算每个时间点的流速，再求最大值
# 计算每个时间点的流速
speed_all = np.hypot(ds["v_x"], ds["v_y"])  # 计算所有时间点的流速
speed_all = speed_all.values  # 转换为numpy数组
speed_all = speed_all[np.isfinite(speed_all)]  # 去除非数值

if speed_all.size == 0:
    raise ValueError("速度场为空，请检查 ds['v_x'], ds['v_y']")

# 保存所有流速数据
# np.savetxt("speed_all.csv", speed_all, delimiter=",", fmt="%.2f")

# 计算最大流速（使用99%分位数）
vmax = np.nanpercentile(speed_all, 99)  # 使用99%分位数
print(f"vmax: {vmax:.2f} m/s")
print(f"总流速数据点数: {speed_all.size}")
print(f"最大流速: {np.nanmax(speed_all):.2f} m/s")

# 计算ds_mean中的有效流速数据
def calculate_masked_velocity_stats(ds_mean_mask):
    """
    计算掩膜后有效流速数据的统计信息
    
    参数:
        ds_mean_mask: 掩膜后的数据集
    
    返回:
        dict: 包含统计信息的字典
    """
    # 计算流速大小
    speed = np.hypot(ds_mean_mask["v_x"], ds_mean_mask["v_y"])
    
    # 获取有效数据（非NaN）
    valid_speed = speed.values[~np.isnan(speed.values)]
    
    # 获取有效数据的位置
    valid_mask = ~np.isnan(speed.values)
    valid_points_count = np.sum(valid_mask)
    
    # 计算统计信息
    total_points = speed.size
    valid_ratio = (valid_points_count / total_points) * 100 if total_points > 0 else 0
    
    stats = {
        '总网格点数': total_points,
        '有效流速点数': valid_points_count,
        '有效流速比例(%)': round(valid_ratio, 2),
        '平均流速(m/s)': round(np.mean(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '最大流速(m/s)': round(np.max(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '最小流速(m/s)': round(np.min(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '流速标准差(m/s)': round(np.std(valid_speed), 4) if len(valid_speed) > 0 else 0
    }
    
    return stats

# 首先重新打开原始视频，提取一帧 RGB 图像并绘制
da_rgb = video.get_frames(method="rgb")
# 投影 RGB 帧
da_rgb_proj = da_rgb.frames.project()
# 绘制第一帧（我们只有一个）不使用任何参数，默认使用"local"模式
p = da_rgb_proj[30].frames.plot()

# 现在将结果绘制在上面，我们使用平均值，因为我们无法绘制超过 2 个维度。
# 默认绘图方法是"quiver"，但也可以使用"scatter"或"pcolormesh"。
# 我们添加了一个漂亮的色条来理解数值大小。
# 我们将 .frames.plot 返回的可视化对象现有的坐标轴句柄作为绘图目标，并使用一定的透明度。
ds_mean = ds.mean(dim="time", keep_attrs=True) # 按时间维度求平均值并保留原始数据的属性信息

# 首先一个 pcolormesh
ds_mean.velocimetry.plot.pcolormesh(
    ax=p.axes,
    alpha=0.3,
    cmap="rainbow",
    add_colorbar=True,
    vmax=vmax
)

ds_mean.velocimetry.plot(
    ax=p.axes,
    color="w",
    alpha=0.5,
)

# # 再次先绘制 rgb 帧
# p = da_rgb_proj[0].frames.plot()

# # 然后绘制掩膜速度图
# ds_mean.velocimetry.plot(
#     ax=p.axes,
#     alpha=0.4,
#     norm=Normalize(vmax=vmax, clip=False),
#     add_colorbar=True
# )

# 计算ds_mean_mask的统计信息
print("=== 掩膜后流速数据统计 ===")
stats = calculate_masked_velocity_stats(ds_mean)

for key, value in stats.items():
    print(f"{key}: {value}")

# 使用pyorc自带的各种掩膜方法对速度数据进行过滤
ds_mask = copy.deepcopy(ds)
mask_corr = ds_mask.velocimetry.mask.corr(inplace=True)
mask_minmax = ds_mask.velocimetry.mask.minmax(s_min=0.01, inplace=True)
mask_rolling = ds_mask.velocimetry.mask.rolling(inplace=True)
mask_outliers = ds_mask.velocimetry.mask.outliers(inplace=True)
mask_var = ds_mask.velocimetry.mask.variance(inplace=True)
mask_angle = ds_mask.velocimetry.mask.angle(inplace=True)
mask_window_nan = ds_mask.velocimetry.mask.window_nan(inplace=True)
mask_count = ds_mask.velocimetry.mask.count(tolerance=0.01, inplace=True)

# 再次应用绘图，这次我们忽略标量值，并让箭头图比之前更美观。
ds_mean_mask = ds_mask.mean(dim="time", keep_attrs=True)

# 计算ds_mean_mask的统计信息
print("=== 掩膜后流速数据统计 ===")
stats = calculate_masked_velocity_stats(ds_mean_mask)

for key, value in stats.items():
    print(f"{key}: {value}")



# 查看流速数据基本情况
for name, da in [('v_x', ds_mean_mask.v_x), ('v_y', ds_mean_mask.v_y)]:
    # 1) 维度
    shape = da.shape
    # 2) NaN 计数
    n_nan = da.isnull().sum().item()      # 总 NaN 数
    n_tot = da.size                       # 总元素数
    ratio = n_nan / n_tot * 100

    print(f"{name}:")
    print(f"  shape   : {shape}")
    print(f"  NaN #   : {n_nan} / {n_tot}")
    print(f"  NaN %%   : {ratio:.2f}%\n")

# 计算出来完之后流速的vmax
speed_masked = np.hypot(ds_mask["v_x"], ds_mask["v_y"])  # 计算所有时间点的流速
speed_masked = speed_masked.values  # 转换为numpy数组
speed_masked = speed_masked[np.isfinite(speed_masked)]  # 去除非数值

if speed_masked.size == 0:
    raise ValueError("速度场为空，请检查 ds_mask['v_x'], ds_mask['v_y']")

# 保存所有流速数据
# np.savetxt("speed_masked.csv", speed_masked, delimiter=",", fmt="%.2f")

# 计算最大流速（使用99%分位数）
vmax = np.nanpercentile(speed_masked, 99)  # 使用97%分位数
print(f"vmax: {vmax:.2f} m/s")
print(f"总流速数据点数: {speed_masked.size}")
print(f"最大流速: {np.nanmax(speed_masked):.2f} m/s")

# 再次先绘制 rgb 帧
p = da_rgb_proj[0].frames.plot()

# 然后绘制掩膜速度图
ds_mean_mask.velocimetry.plot(
    ax=p.axes,
    alpha=0.4,
    norm=Normalize(vmax=vmax, clip=False),
    add_colorbar=True
)

# 计算ds_mean_mask的统计信息
print("=== 掩膜后流速数据统计 ===")
stats = calculate_masked_velocity_stats(ds_mean_mask)

for key, value in stats.items():
    print(f"{key}: {value}")