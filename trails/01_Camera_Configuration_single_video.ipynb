import xarray as xr
import pyorc
import cartopy
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# uncomment line below if you want to view coordinates interactively
#%matplotlib notebook
video_file = "yuwangling/0812_speed.mp4"
video = pyorc.Video(video_file, start_frame=0, end_frame=1)  # we only need one frame
frame = video.get_frame(0, method="rgb")

# plot frame on a notebook-style window
f = plt.figure(figsize=(10, 6))
plt.imshow(frame)



%matplotlib inline
gcps = dict(
    src=[
        [1260, 180], # 右下角
        [1000, 130], # 右上角
        [75, 80], # 左上角
        [745, 500] # 左下角
    ]
)

f = plt.figure(figsize=(16, 9))
plt.imshow(frame)
plt.plot(*zip(*gcps["src"]), "rx", markersize=20, label="Control points")
plt.legend()


from pyproj import Transformer

# 定义转换器：WGS84 (EPSG:4326) -> UTM 51N (EPSG:32651)
transformer = Transformer.from_crs("EPSG:4326", "EPSG:32651", always_xy=True)

# 原始 GCPs（按右下、右上、左上、左下顺序）
gcps_wgs84 = [
    [121.746625, 29.858377],  # 右下角
    [121.746658, 29.858407],  # 右上角
    [121.746700, 29.858496],  # 左上角
    [121.746569, 29.858501]   # 左下角
]

# 转换为 UTM
gcps_utm = [list(transformer.transform(lon, lat)) for lon, lat in gcps_wgs84]

print("UTM Coordinates (EPSG:32651):")
for i, (utm, orig) in enumerate(zip(gcps_utm, gcps_wgs84)):
    print(f"{utm}  # {['右下角', '右上角', '左上角', '左下角'][i]} {orig}")

# first add our UTM 35S coordinates. This MUST be in precisely the same order as the src coordinates.
# gcps["dst"] = [
#     [121.757441,29.861693],  # 右下角
#     [121.757499,29.861697],  # 右上角
#     [121.757462,29.861779],  # 左上角
#     [121.757370,29.861759]   # 左下角
# ]
gcps["dst"] = [
    [378938.6998393936, 3303751.845373942],  # 右下角 [121.746625, 29.858377]
    [378941.92372252623, 3303755.1352278604],  # 右上角 [121.746658, 29.858407]
    [378946.08818368637, 3303764.953945649],  # 左上角 [121.7467, 29.858496]
    [378933.44014820986, 3303765.6458775243]  # 左下角 [121.746569, 29.858501]
]

# # if we would use this video as survey in video, the lines below are also needed, 
# # and proper values need to be filled in. They are now commented out.
# gcps["h_ref"] = <your locally measured water level during survey in>
gcps["z_0"] = 5.0

# set the height and width
height, width = frame.shape[0:2]

# now we use everything to make a camera configuration
cam_config = pyorc.CameraConfig(height=height, width=width, gcps=gcps, crs=32651) # 32651 is 经纬度

from trails.utils.gaode import *
from trails.utils.common import test_network_connection

# 运行测试
print("=== 网络连接测试 ===")
test_network_connection()
print("\n=== 高德服务测试 ===")
test_gaode_connection()

# 先测试API
your_api_key = "6cae25fa60c422a5e79c1ce874f0424a"
print(len(your_api_key))
test_gaode_api(your_api_key)


# 使用静态地图
ax = plot_camera_config_with_gaode(cam_config, api_key=your_api_key, method="static", save_path="static_map_gaode.png")
plt.show()

from trails.utils.baidu import *
test_baidu_connection()

# 测试百度地图api
your_api_key = "irMGPfRrhNNPx0clwYHRVbaT7SPQohup"
print(len(your_api_key))
test_baidu_api(your_api_key)

# 正式画图
ax = plot_camera_config_with_baidu(cam_config, api_key=your_api_key, method="static", save_path="static_map_baidu.png")
plt.show()

corners = [
    [775, 600], # 左下角
    [75, 80], # 左上角
    [750, 70], # 右上角
    [1240, 140] # 右下角
]
cam_config.set_bbox_from_corners(corners)
cam_config.resolution = 0.01
cam_config.window_size = 25

print(f"bbox: {cam_config.bbox}, rvec: {cam_config.rvec}, tvec: {cam_config.tvec}")


f = plt.figure(figsize=(10, 6))
plt.imshow(frame)
plt.plot(*zip(*gcps["src"]), "rx", markersize=20, label="Control points")
plt.plot(*zip(*corners), "co", label="Corners of AOI")
plt.legend()

print(cam_config)
cam_config.to_file("yuwangling/yuwangling.json")

