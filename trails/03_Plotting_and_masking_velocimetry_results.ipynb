import xarray as xr
import pyorc
from matplotlib.colors import Normalize

ds = xr.open_dataset("yuwangling_piv.nc")

import pandas as pd

df_vx = ds['v_x'].to_dataframe().reset_index()                                               
df_vy = ds['v_y'].to_dataframe().reset_index() 

# 合并数据                                                                                   
df_combined = pd.DataFrame({                                                                 
    'time': df_vx['time'],                                                                   
    'y': df_vx['y'],                                                                         
    'x': df_vx['x'],                                                                         
    'v_x': df_vx['v_x'],                                                                     
    'v_y': df_vy['v_y']                                                                      
})

df_combined.to_csv('velocity_data.csv', index=False)                                         
print('流速数据已保存到 velocity_data.csv')                                                  
print('数据形状:', df_combined.shape)                                                        
print('前5行数据:')                                                                          
print(df_combined.head())

# 流速数据分析和可视化
from utils.velocity_analysis import analyze_velocity_data

# 使用我们创建的分析工具进行流速分析
df_velocity, top_points = analyze_velocity_data('velocity_data.csv', n_points=5)

import numpy as np

# 正确的做法：先计算每个时间点的流速，再求最大值
# 计算每个时间点的流速
speed_all = np.hypot(ds["v_x"], ds["v_y"])  # 计算所有时间点的流速
speed_all = speed_all.values  # 转换为numpy数组
speed_all = speed_all[np.isfinite(speed_all)]  # 去除非数值

if speed_all.size == 0:
    raise ValueError("速度场为空，请检查 ds['v_x'], ds['v_y']")

# 保存所有流速数据
# np.savetxt("speed_all.csv", speed_all, delimiter=",", fmt="%.2f")

# 计算最大流速（使用99%分位数）
vmax = np.nanpercentile(speed_all, 99)  # 使用99%分位数
print(f"vmax: {vmax:.2f} m/s")
print(f"总流速数据点数: {speed_all.size}")
print(f"最大流速: {np.nanmax(speed_all):.2f} m/s")

# 首先重新打开原始视频，提取一帧 RGB 图像并绘制
video_file = "yuwangling/ch01_20250414145900.mp4"

video = pyorc.Video(video_file, start_frame=0, end_frame=125)
# 从速度测量结果中借用相机配置
video.camera_config = ds.velocimetry.camera_config

da_rgb = video.get_frames(method="rgb")
# 投影 RGB 帧
da_rgb_proj = da_rgb.frames.project()
# 绘制第一帧（我们只有一个）不使用任何参数，默认使用"local"模式
p = da_rgb_proj[30].frames.plot()

# 现在将结果绘制在上面，我们使用平均值，因为我们无法绘制超过 2 个维度。
# 默认绘图方法是"quiver"，但也可以使用"scatter"或"pcolormesh"。
# 我们添加了一个漂亮的色条来理解数值大小。
# 我们将 .frames.plot 返回的可视化对象现有的坐标轴句柄作为绘图目标，并使用一定的透明度。
ds_mean = ds.mean(dim="time", keep_attrs=True)

# 首先一个 pcolormesh
ds_mean.velocimetry.plot.pcolormesh(
    ax=p.axes,
    alpha=0.3,
    cmap="rainbow",
    add_colorbar=True,
    vmax=3.17
)

ds_mean.velocimetry.plot(
    ax=p.axes,
    color="w",
    alpha=0.5,
)


import numpy as np

for name, da in [('v_x', ds_mean.v_x), ('v_y', ds_mean.v_y)]:
    # 1) 维度
    shape = da.shape
    # 2) NaN 计数
    n_nan = da.isnull().sum().item()      # 总 NaN 数
    n_tot = da.size                       # 总元素数
    ratio = n_nan / n_tot * 100

    print(f"{name}:")
    print(f"  shape   : {shape}")
    print(f"  NaN #   : {n_nan} / {n_tot}")
    print(f"  NaN %%   : {ratio:.2f}%\n")

import copy
ds_mask = copy.deepcopy(ds)
mask_corr = ds_mask.velocimetry.mask.corr(inplace=True)
mask_minmax = ds_mask.velocimetry.mask.minmax(s_min=0.01, inplace=True)
mask_rolling = ds_mask.velocimetry.mask.rolling(inplace=True)
mask_outliers = ds_mask.velocimetry.mask.outliers(inplace=True)
mask_var = ds_mask.velocimetry.mask.variance(inplace=True)
mask_angle = ds_mask.velocimetry.mask.angle(inplace=True)
mask_window_nan = ds_mask.velocimetry.mask.window_nan(inplace=True)
# mask_count = ds_mask.velocimetry.mask.count(tolerance=0.01, inplace=True)


# 再次应用绘图，这次我们忽略标量值，并让箭头图比之前更美观。
ds_mean_mask = ds_mask.mean(dim="time", keep_attrs=True)

# 查看流速数据基本情况
for name, da in [('v_x', ds_mean_mask.v_x), ('v_y', ds_mean_mask.v_y)]:
    # 1) 维度
    shape = da.shape
    # 2) NaN 计数
    n_nan = da.isnull().sum().item()      # 总 NaN 数
    n_tot = da.size                       # 总元素数
    ratio = n_nan / n_tot * 100

    print(f"{name}:")
    print(f"  shape   : {shape}")
    print(f"  NaN #   : {n_nan} / {n_tot}")
    print(f"  NaN %%   : {ratio:.2f}%\n")

# 计算出来完之后流速的vmax
speed_masked = np.hypot(ds_mask["v_x"], ds_mask["v_y"])  # 计算所有时间点的流速
speed_masked = speed_masked.values  # 转换为numpy数组
speed_masked = speed_masked[np.isfinite(speed_masked)]  # 去除非数值

if speed_masked.size == 0:
    raise ValueError("速度场为空，请检查 ds_mask['v_x'], ds_mask['v_y']")

# 保存所有流速数据
# np.savetxt("speed_masked.csv", speed_masked, delimiter=",", fmt="%.2f")

# 计算最大流速（使用99%分位数）
vmax = np.nanpercentile(speed_masked, 95)  # 使用95%分位数
print(f"vmax: {vmax:.2f} m/s")
print(f"总流速数据点数: {speed_masked.size}")
print(f"最大流速: {np.nanmax(speed_masked):.2f} m/s")


# 手工过滤掉超过vmax的值
print(f"过滤前v_x形状: {ds_mean_mask.v_x.shape}, 过滤前v_y形状: {ds_mean_mask.v_y.shape}")

# 计算速度大小
speed_mean = np.hypot(ds_mean_mask["v_x"], ds_mean_mask["v_y"])

# 创建掩膜：速度超过vmax的位置设为NaN
mask_over_vmax = speed_mean > vmax

# 应用掩膜
ds_mean_mask = ds_mean_mask.copy()
ds_mean_mask["v_x"] = ds_mean_mask["v_x"].where(~mask_over_vmax)
ds_mean_mask["v_y"] = ds_mean_mask["v_y"].where(~mask_over_vmax)

# 检查过滤效果
print(f"过滤后v_x形状: {ds_mean_mask.v_x.shape}, 过滤后v_y形状: {ds_mean_mask.v_y.shape}")

# 统计被过滤的点数
n_filtered = mask_over_vmax.sum().item()
total_points = mask_over_vmax.size
print(f"过滤掉的点数: {n_filtered} / {total_points} ({n_filtered/total_points*100:.2f}%)")

# 检查实际速度范围
speed_actual = np.hypot(ds_mean_mask["v_x"], ds_mean_mask["v_y"])
print(f"最大速度值: {speed_actual.max().item():.2f}")
print(f"速度范围: {speed_actual.min().item():.2f} - {speed_actual.max().item():.2f}")

# 如果确实还有高速值，检查过滤条件
print(f"过滤阈值vmax: {vmax}")
print(f"超过阈值的比例: {(speed_actual > vmax).sum().item() / speed_actual.size * 100:.2f}%")

# 再次先绘制 rgb 帧
p = da_rgb_proj[0].frames.plot()

# 然后绘制掩膜速度图
ds_mean_mask.velocimetry.plot(
    ax=p.axes,
    alpha=0.4,
    norm=Normalize(vmax=vmax, clip=False),
    add_colorbar=True
)


# 计算掩膜后有效数据的统计信息
import numpy as np

# 计算ds_mean_mask中的有效流速数据
def calculate_masked_velocity_stats(ds_mean_mask):
    """
    计算掩膜后有效流速数据的统计信息
    
    参数:
        ds_mean_mask: 掩膜后的数据集
    
    返回:
        dict: 包含统计信息的字典
    """
    # 计算流速大小
    speed = np.hypot(ds_mean_mask["v_x"], ds_mean_mask["v_y"])
    
    # 获取有效数据（非NaN）
    valid_speed = speed.values[~np.isnan(speed.values)]
    
    # 获取有效数据的位置
    valid_mask = ~np.isnan(speed.values)
    valid_points_count = np.sum(valid_mask)
    
    # 计算统计信息
    total_points = speed.size
    valid_ratio = (valid_points_count / total_points) * 100 if total_points > 0 else 0
    
    stats = {
        '总网格点数': total_points,
        '有效流速点数': valid_points_count,
        '有效流速比例(%)': round(valid_ratio, 2),
        '平均流速(m/s)': round(np.mean(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '最大流速(m/s)': round(np.max(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '最小流速(m/s)': round(np.min(valid_speed), 4) if len(valid_speed) > 0 else 0,
        '流速标准差(m/s)': round(np.std(valid_speed), 4) if len(valid_speed) > 0 else 0
    }
    
    return stats

# 计算ds_mean_mask的统计信息
print("=== 掩膜后流速数据统计 ===")
stats = calculate_masked_velocity_stats(ds_mean_mask)

for key, value in stats.items():
    print(f"{key}: {value}")

import numpy as np

for name, da in [('v_x', ds_mean_mask.v_x), ('v_y', ds_mean_mask.v_y)]:
    # 1) 维度
    shape = da.shape
    # 2) NaN 计数
    n_nan = da.isnull().sum().item()      # 总 NaN 数
    n_tot = da.size                       # 总元素数
    ratio = n_nan / n_tot * 100

    print(f"{name}:")
    print(f"  shape   : {shape}")
    print(f"  NaN #   : {n_nan} / {n_tot}")
    print(f"  NaN %%   : {ratio:.2f}%\n")

# 在时域中应用所有方法，并使用宽松的角度掩码
import numpy as np
ds_mask2 = copy.deepcopy(ds)
ds_mask2.velocimetry.mask.corr(inplace=True)
ds_mask2.velocimetry.mask.minmax(inplace=True)
ds_mask2.velocimetry.mask.rolling(inplace=True)
ds_mask2.velocimetry.mask.outliers(inplace=True)
ds_mask2.velocimetry.mask.variance(inplace=True)
ds_mask2.velocimetry.mask.angle(angle_tolerance=0.5*np.pi)
ds_mask2.velocimetry.mask.count(inplace=True)
ds_mask2.velocimetry.mask.window_mean(wdw=2, inplace=True, tolerance=0.5, reduce_time=True)

# 现在首先在时间维度上进行平均，然后再应用任何仅在空间维度上工作的滤波器。
ds_mean_mask2 = ds_mask2.mean(dim="time", keep_attrs=True)

# 再次应用绘图
# 再次是 RGB 帧
p = da_rgb_proj[0].frames.plot()

#...然后是过滤后的速度测量
ds_mean_mask2.velocimetry.plot(
    ax=p.axes,
    alpha=0.4,
    norm=Normalize(vmax=0.6, clip=False),
    add_colorbar=True
)


# 再次绘制 RGB 帧。但现在我们使用“地理”模式在地图上绘制
p = da_rgb_proj[0].frames.plot(mode="geographical")

# 然后再次进行掩膜速度测量，但也是地理模式
ds_mean_mask2.velocimetry.plot(
    ax=p.axes,
    mode="geographical",
    alpha=0.4,
    norm=Normalize(vmax=0.6, clip=False),
    add_colorbar=True
)

# 也可以从 cartopy 添加一个卫星背景
import cartopy.io.img_tiles as cimgt
import cartopy.crs as ccrs
tiles = cimgt.GoogleTiles(style="satellite")
p.axes.add_image(tiles, 19)
# 稍微缩小一点，以便我们实际上能看到一点
p.axes.set_extent([
    da_rgb_proj.lon.min() - 0.00005,
    da_rgb_proj.lon.max() + 0.00005,
    da_rgb_proj.lat.min() - 0.00005,
    da_rgb_proj.lat.max() + 0.00005],
    crs=ccrs.PlateCarree()
)


# 再次使用 rgb 框架，但这次是未投影的。现在我们使用"相机"模式来绘制相机视角
p = da_rgb[0].frames.plot(mode="camera")

# 然后再次使用掩膜速度计，但也是相机。这给我们提供了一个增强现实视图。箭头比例需要调整以适应屏幕显示 
ds_mean_mask.velocimetry.plot(
    ax=p.axes,
    mode="camera",
    alpha=0.4,
    norm=Normalize(vmin=0., vmax=0.6, clip=False),
    add_colorbar=True
)


ds_mask2.velocimetry.set_encoding()
ds_mask2.to_netcdf("yuwangling_masked.nc")

print(ds_mask2)

