## 核心参数
1. 'camera_matrix' - 参考笔记本'05_Camera_calibration.ipynb'中的方法，使用棋盘格标定视频通过cam_config.set_lens_calibration()函数自动计算获得
2. 'gcps['src']'：在笔记本'01_Camera_Configuration_single_video.ipynb'中，通过在视频帧上手动点击标记点获取像素坐标
3. 'gcps['dst']'：使用RTK GPS设备实地测量控制点的UTM 35S坐标（EPSG:32735）
4. 'height', 'width' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中，通过frame.shape[0:2]从视频帧自动获取
5. 'crs' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中手动设置为32735（UTM 35S坐标系统）
6. 'resolution' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中手动设置为0.01米（1厘米分辨率）
7. 'window_size' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中手动设置为25像素
8. 'gcps['z_0']' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中，使用RTK GPS测量获得，设为1182.2米
9. 'gcps['h_ref']' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中，对于单视频处理设为0.0（仅在多视频不同水位时需要）
10. 'dist_coeffs' - 参考笔记本'05_Camera_calibration.ipynb'，通过镜头标定自动计算获得
11. 'lens_position' - 在配置中设为null，仅在固定摄像头多水位处理时需要
12. 'is_nadir' - 设为false，表示非垂直拍摄
13. 'bbox' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中，通过cam_config.set_bbox_from_corners(corners)从四个角点坐标自动计算获得
14. 'rvec', 'tvec': 旋转向量、平移向量，分别是3个元素表示相机相对于世界坐标系的旋转，通过罗德里格斯公式转换为3×3旋转矩阵，3个元素表示相机中心在世界坐标系中的位置；但是只要有'gcps['src']', 'gcps['dst']','camera_matrix', 'lens_position'就不需要单独配置

## 一些情况说明
1. `cam_config.to_file("filename.json")` - 将完整的CameraConfig对象保存为JSON文件
2. bbox产生差异的原因：
    - 角点坐标的微小差异：在`set_bbox_from_corners`方法中，角点坐标的微小变化会影响最终bbox
    - 坐标变换过程：该方法会调用unproject_points将像素坐标反投影到真实世界坐标，然后通过cv.get_aoi创建规整的边界框
    - 分辨率对齐：resolution=0.01的设置会影响网格对齐方式
    - 舍入误差：在坐标变换和计算过程中存在浮点数舍入误差
    这种差异在实际应用中是可以接受的，因为：
    - 角点坐标差异在厘米级别
    - 不影响最终的流速测量精度
    - 是计算过程中的正常现象

## 当前情况记录

### 参数相关
1. 'camera_matrix', 'lens_position': 都要靠`examples/05_Camera_calibration.ipynb`里的方法。目前录的视频不可靠，需要重新录制。
2. 'gcps['src']'：通过在视频帧上手动点击标记点获取像素坐标。
3. 'gcps['dst']'：参照'gcps['src']'的坐标，最好是利用RTK GPS设备在现场标记。但是目前没有该设备，所以利用手机的GPS设备进行标记的经纬度，后续手工转化为UTM 355坐标体系的数值
4. 'gcps['z_0']'：利用RTK GPS设备在现场测量水面的海拔高度。但是目前没有该设备，所以利用手机的GPS设备进行标记。
5. 'gcps['h_ref']'：水面的海拔高度，设为0.0。
6. 'height', 'width'：从视频帧中获取。
7. 'crs'：设为32735（UTM 35S坐标系统）。
8. 'resolution', 'window', 'lens_position', 'is_nadir': 都采用默认值
9. 'bbox' - 在笔记本'01_Camera_Configuration_single_video.ipynb'中，通过cam_config.set_bbox_from_corners(corners)从四个角点坐标自动计算获得
10. 'rvec', 'tvec': 只要有'gcps['src']', 'gcps['dst']','camera_matrix', 'lens_position'就不需要单独配置

#### 用gps app到现场测量的经纬度
```
[121.746625, 29.858377],  # 右下角
[121.746658, 29.858407],  # 右上角
[121.746700, 29.858496],  # 左上角
[121.746569, 29.858501]  # 左下角
```

#### 在百度经纬度抽取的网页上测量
```
[121.757441,29.861693],  # 右下角
[121.757499,29.861697],  # 右上角
[121.757462,29.861779],  # 左上角
[121.757370,29.861759]  # 左下角
```

### 结果相关
1. 算出来的流速单位是m/s

## 画图相关

### 育王岭
1. 水面摄像头的角度基本是从北向南拍的，所以只要拿坐标系调整后图像一定是倒置的