# PyORC 索引越界错误调试指南

## 问题描述

在使用PyORC进行图像投影时，可能会遇到以下错误：

```
IndexError: index 1154695 is out of bounds for axis 0 with size 921600
```

这个错误通常发生在 `img_to_ortho` 函数中，表示计算出的图像索引超出了实际图像的边界。

## 错误原因分析

根据错误信息和日志分析，主要原因是：

1. **相机配置与实际图像尺寸不匹配**：相机配置文件中设置的 `width` 和 `height` 与实际输入图像的尺寸不一致
2. **索引计算错误**：在将2D图像坐标转换为1D索引时，使用了错误的图像尺寸参数
3. **投影参数问题**：相机标定参数或GCP（地面控制点）设置可能不正确

## 调试步骤

### 1. 使用诊断工具

我们已经添加了详细的调试信息和诊断工具。在您的代码中添加以下诊断：

```python
from tests.debug_index_error import diagnose_camera_config_mismatch

# 在出错的地方添加诊断
diagnose_camera_config_mismatch(da, cc, x, y, z)
```

### 2. 检查日志输出

运行代码时，注意以下调试信息：

- `[DEBUG] Camera dimensions - width: X, height: Y`：相机配置的尺寸
- `[DEBUG] Input data array shape: (H, W)`：实际图像的尺寸
- `[WARNING] Found N out-of-bounds indices!`：发现超出边界的索引

### 3. 对比官方示例

对比您的数据与官方示例的差异：

**您的数据：**
- 图像形状：(1609, 3078) → 921600 像素
- idx_img 数量：4354454

**官方示例：**
- 图像形状：(820, 960) → 787200 像素  
- idx_img 数量：738534

## 解决方案

### 方案1：更新相机配置

如果诊断显示图像尺寸不匹配，请更新相机配置文件：

```python
# 假设您的实际图像尺寸是 1609 x 3078
cc.width = 3078
cc.height = 1609
```

### 方案2：检查图像预处理

确保输入的图像数据与相机配置一致：

```python
print(f"图像形状: {da.shape}")
print(f"相机配置: width={cc.width}, height={cc.height}")
```

### 方案3：验证相机标定

检查相机标定参数是否正确：

```python
print(f"相机矩阵: {cc.camera_matrix}")
print(f"畸变系数: {cc.dist_coeffs}")
print(f"GCP数量: {len(cc.gcps['src'])}")
```

## 代码修改说明

我们已经在以下文件中添加了调试信息：

### 1. `pyorc/api/cameraconfig.py`

在 `map_idx_img_ortho` 方法中添加了：
- 相机尺寸信息输出
- 索引范围检查
- 超出边界索引的警告

### 2. `pyorc/project.py`

在 `img_to_ortho` 函数中添加了：
- 边界检查逻辑
- 详细的错误信息
- 自动过滤无效索引的机制

在 `project_numpy` 函数中添加了：
- 输入数据形状检查
- 相机配置验证

## 预防措施

1. **验证相机配置**：确保配置文件中的图像尺寸与实际图像匹配
2. **检查数据一致性**：在处理前验证所有输入数据的一致性
3. **使用诊断工具**：定期使用诊断工具检查配置正确性

## 常见问题

**Q: 为什么官方示例可以运行，我的数据不行？**
A: 不同的数据集有不同的图像尺寸和相机配置。需要确保您的相机配置与实际数据匹配。

**Q: 如何确定正确的相机配置参数？**
A: 检查您的原始图像文件，获取实际的宽度和高度，然后更新相机配置。

**Q: 修改后仍然有问题怎么办？**
A: 请检查相机标定参数和GCP设置是否正确，这些参数直接影响投影计算的准确性。

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误日志
2. 图像数据的尺寸信息
3. 相机配置文件内容
4. 诊断工具的输出结果
