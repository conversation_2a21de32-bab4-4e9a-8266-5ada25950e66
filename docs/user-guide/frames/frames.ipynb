{"cells": [{"cell_type": "markdown", "id": "35e02afb", "metadata": {}, "source": ["### Frames\n", "A frames object is literally a ``xr.DataA<PERSON>y`` object with certain properties and an expected structure with\n", "coordinates and variables. In this section, we show what you can do with such an object. \n", "\n", "As shown in the videos section, such an object can be directly derived from a video object using the method ``video.get_frames``. An example of the ``xr.DataArray`` structure returned is shown below, with an ``method=\"rgb\"`` setting to get a color image back (default is grayscale)."]}, {"cell_type": "code", "execution_count": null, "id": "4687383e", "metadata": {}, "outputs": [], "source": ["import pyorc\n", "import numpy as np\n", "# set a video filename below, change to your own local file location\n", "video_file = \"../../../examples/ngwerere/ngwerere_20191103.mp4\"\n", "# point to a file containing the camera configuration\n", "cam_config = pyorc.load_camera_config(\"../../../examples/ngwerere/ngwerere.json\")\n", "video = pyorc.Video(\n", "    video_file,\n", "    camera_config=cam_config,\n", "    start_frame=0,\n", "    end_frame=125,\n", "    stabilize=\"fixed\"\n", ")\n", "da_frames = video.get_frames(method=\"rgb\")\n", "da_frames\n"]}, {"cell_type": "markdown", "id": "95db853d", "metadata": {}, "source": ["It can be seen that this ``xr.DataArray`` contains a data blob with all the frames (i.e. ``time: 125`` meaning we have 125 frames at our disposal for the further analysis) and a ``x`` and ``y`` coordinate system, which simply is a column and row count for the pixels in the image objective. We are looking an HD video here, and so this column and row count is 1920x1080. We also have a dimension ``rgb`` because we hold 3 channels for each image. If you would call ``get_frames`` without any arguments, you would not get this. A few things are noteworthy to further explain about some of the coordinates and attributes.\n", "\n", "- ``time``: contains an axis which measures the time since the first frame in seconds. It is based on the frames per second property of the video.\n", "- ``xp`` and ``yp`` are very similar to ``x`` and ``y``, but they contain a mesh of row, column coordinates, instead of only a 1-dimensional axis. This is required for some of the methods that can be applied on a frames ``xr.DataArray``.\n", "- ``camera_config``: crucial for a frames ``xr.DataArray`` is to have understanding of the camera's properties and perspective and relationship with geography. This is all stored in your camera configuration, hence, this camera configuration is also provided to the ``xr.DataArray`` storing your frames. It will be used for further processing steps, in particular reprojection.\n"]}, {"cell_type": "markdown", "id": "86cd52fd", "metadata": {}, "source": ["#### Methods for frames"]}, {"cell_type": "markdown", "id": "0029011f", "metadata": {}, "source": ["Any manipulation to a frames object that is really particular to the fact you are looking at a frames ``xr.DataArray`` as opposed to any other ``xr.<PERSON>Array`` can be found behind a subclass called ``frames``. So to call such a functionality, e.g. the plotting method, you basically call ``da_frames.frames.plot``. Let's try that."]}, {"cell_type": "code", "execution_count": null, "id": "395d12fd", "metadata": {}, "outputs": [], "source": ["da_frames.frames.plot()"]}, {"cell_type": "markdown", "id": "595adadd", "metadata": {}, "source": ["That gave an error. You can see that the API gives specific feedback that the ``.frames.plot`` method does not work on a frames array with several time steps. This is simply because you can only plot one step at the time. You can select a step, with normal ``xarray`` methods, which are very similar to ``numpy``, and then try it again."]}, {"cell_type": "code", "execution_count": null, "id": "3b3ccd63", "metadata": {}, "outputs": [], "source": ["# only plot time index 0\n", "da_frames[0].frames.plot()"]}, {"cell_type": "markdown", "id": "baf11863", "metadata": {}, "source": ["#### Improving contrast\n", "There are a number of ways to improve contrast of moving features. These so far include the following methods:\n", "\n", "- ``frames.normalization``: removes the average of a number of frames in time. This then yields a better contrast of moving things, compared to static things. This is particularly useful to remove visuals of the bottom, when you have very transparent water. You can set the amount of frames used for averaging.\n", "- ``frames.edge_detect``: enhances the visibility of strong spatial gradients in the frame, by applying two kernel filters on the individual frames with varying window sizes, and returning the difference between the filtered images.\n", "\n", "These operations apply the filter on all frames, and return the result again as a frames ``xr.DataArray``, but then with the filters applied. They can only be applied on single channel (i.e. greyscale) images.\n", "\n", "We show a result of applying both filters on the first frame below."]}, {"cell_type": "code", "execution_count": null, "id": "a08c1aef", "metadata": {}, "outputs": [], "source": ["da_grey = video.get_frames()\n", "da_norm = da_grey.frames.normalize()\n", "da_edge = da_norm.frames.edge_detect(wdw_1=6, wdw_2=10)\n", "da_edge[0].frames.plot(vmin=-10, vmax=10, cmap=\"RdBu_r\")\n"]}, {"cell_type": "markdown", "id": "2e2aa02e", "metadata": {}, "source": ["#### Projection\n", "Essential for velocity estimation is the projection part. If you have a proper ``CameraConfig`` in your video, which includes the intrinsic matrix and possible distortion coefficients of your camera lens, and ground control points that gives pairs of real-world 3D coordinates and coordinates as they are in your 2D objective, then you can reproject your data into real-world coordinates. For PIV typically, we recommend to reproject to a resolution of 2 to 5 cm (i.e. 0.02 to 0.05). 5 cm is in our expertise usually sufficient, but dependent on the scale of structure that you may wish to follow, you may have to change this into a lower resolution. Let's have a look at a reprojected image. Note that this can also be done in RGB."]}, {"cell_type": "code", "execution_count": null, "id": "0cd82db3", "metadata": {}, "outputs": [], "source": ["# let's retrieve rgb frames\n", "da_rgb = video.get_frames(method=\"rgb\")\n", "# now reproject\n", "da_proj = da_rgb.frames.project()\n", "# and plot!\n", "da_proj[0].frames.plot()\n"]}, {"cell_type": "markdown", "id": "a9a2c0b2", "metadata": {}, "source": ["The ``edge_detection`` filter can better be applied on already projected images, in order to ensure that the window sizes set for the filters are in the real spatial domain. The reprojection above is 0.01 meter in resolution, hence if you wish to reveal edges of structure of a few centimeters in size, the filter sizes should be in that range as well. An example with window sizes of 2 and 4 pixels (i.e. 2 and 4 centimeter) on projected grayscale images is given below."]}, {"cell_type": "code", "execution_count": null, "id": "d02edc5b", "metadata": {}, "outputs": [], "source": ["# repeat the reprojection, but now with normalizes images, and with greyscale.\n", "# let's retrieve rgb frames\n", "da_grey = video.get_frames()\n", "# normalize first. This can be done in the original objective\n", "da_norm = da_grey.frames.normalize()\n", "# now reproject\n", "da_proj = da_norm.frames.project()\n", "# after reprojection, do edge detection\n", "da_edge = da_proj.frames.edge_detect(wdw_1=2, wdw_2=4)\n", "# and plot!\n", "da_edge[0].frames.plot(vmin=-10, vmax=10, cmap=\"RdBu_r\")\n", "\n"]}, {"cell_type": "markdown", "id": "2ba70aad", "metadata": {}, "source": ["#### Plotting\n", "Geographical awareness is a really important feature of the frames ``xr.DataArray`` objects. So far we performed plotting in a local projection. The figure above has units meters on both the x-axis and y-axis. But after projection, geographical projections are also stored if the user supplied a coordinate reference system in the ``CameraConfig``. Below you can see that new coordinate variables ``lon`` and ``lat`` are available on all project ``xr.DataArray`` variables. The ``mode`` option with value ``geographical`` is used to decide which mode to use."]}, {"cell_type": "code", "execution_count": null, "id": "22eba2dc", "metadata": {}, "outputs": [], "source": ["da_edge"]}, {"cell_type": "markdown", "id": "f12ac192", "metadata": {}, "source": ["These new coordinate variables can be interpreted by the ``.frames.plot`` functionality to also make a geographical plot, which can be combined with other geographical data you may have,m such as background maps, OpenStreetMap, satellite background and so on. The ``cartopy`` package is used to provide this geographical awareness to your plots. We refer to [cartopy's documentation](https://scitools.org.uk/cartopy/docs/latest/) for more information. An example of geographical plots and the camera's objective plot are provided below. Note that the ``.frames.plot`` function always returns the mappable. This can then be used to retrieve the axes of the mappable for instance to add other things to that axes, or to add a legend or colorbar."]}, {"cell_type": "code", "execution_count": null, "id": "dcbab882", "metadata": {"scrolled": true}, "outputs": [], "source": ["import cartopy.io.img_tiles as cimgt\n", "import cartopy.crs as ccrs\n", "# when using mode=\"geographical\" and not supplying an axes, the plot function will make a geogrpahical aware axes\n", "# for you.\n", "p = da_edge[0].frames.plot(mode=\"geographical\", vmin=-10, vmax=10, cmap=\"RdBu_r\")\n", "# tiles = cimgt.OSM()\n", "tiles = cimgt.GoogleTiles(style=\"satellite\")\n", "# add a google satellite image using cartopy functionality\n", "p.axes.add_image(tiles, 19)\n", "# zoom out a little bit so that we can actually see a bit\n", "p.axes.set_extent([\n", "    da_edge.lon.min() - 0.0005,\n", "    da_edge.lon.max() + 0.0005,\n", "    da_edge.lat.min() - 0.0005,\n", "    da_edge.lat.max() + 0.0005],\n", "    crs=ccrs.<PERSON><PERSON>()\n", ")"]}, {"cell_type": "markdown", "id": "275f6771", "metadata": {}, "source": ["#### Animations\n", "You can store the results of your frames operations to a video in two ways:\n", "\n", "- ``.frames.to_ani``: stores an animation of the frames using your provided settings. These are passed to ``matplotlib.pyplot.imshow``. So all inputs to that function you are used to can be used.\n", "- ``.frames.to_video``: this will store the values in a video file using ``opencv``. It means that all values are scaled to between 0 and 255 and only greyscale video will be stored without any axis information. This is useful to quickly check the result of your operations.\n", "\n", "Below we give an example of how these functions work."]}, {"cell_type": "code", "execution_count": null, "id": "df952552", "metadata": {}, "outputs": [], "source": ["# we will only store the first 30 frames\n", "da_sel = da_edge[0:30]\n", "\n", "# first with all color control\n", "da_sel.frames.to_ani(\"edge_ani.mp4\", vmin=-10, vmax=10, cmap=\"RdBu_r\")\n", "\n", "# now to a raw video without control over colors and axis\n", "# we want to maintain the min and max range, so have to explicitly use that\n", "da_scaled = np.maximum(np.minimum(da_sel, 10), -10)\n", "da_scaled.frames.to_video(\"edge_video.mp4\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a16e41f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}