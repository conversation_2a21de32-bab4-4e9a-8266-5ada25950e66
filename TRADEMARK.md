# OpenRiverCam Trademark Guidelines

> **_Acknowledgement:_** These guidelines are loosely based on the guidelines for the trademarks of OpenDroneMap (see [this](https://github.com/OpenDroneMap/documents/blob/master/TRADEMARK.md) link for the OpenDroneMap guidelines).

OpenRiverCam is an open source software ecosystem, founded by a collaborative of [Rainbow Sensing](https://rainbowsensing.com/) and the [Trans-African Hydrometeorological Observatory](https://tahmo.org/) with contributions from [OpenDroneMap.org](https://opendronemap.org/) and [Humanitarian OpenStreetMap Team](https://www.hotosm.org/). Maintenance is organised by [Rainbow Sensing](https://rainbowsensing.com/). Because we make our code available to download and modify, our intellectual property (i.e. recognizing who has established and who is maintaining OpenRiverCam) is essential to us. Through these trademark guidelines, we provide users guidance how to refer to our products whilst using one of them, or building a product or service on top of them. When using OpenRiverCam trademarks (including some product names that are part of our organization) you must comply with these OpenRiverCam Trademark Guidelines.

Our trademark names include:

 * OpenRiverCam
 * pyOpenRiverCam
 * NodeOpenRiverCam
 * LiveOpenRiverCam

Our logos include a color, greyscale and and black-and-white logo as shown below:

 ![ORC](https://raw.githubusercontent.com/localdevices/pyorc/main/docs/_static/orc_logo_color.svg)
 ![ORC](https://raw.githubusercontent.com/localdevices/pyorc/main/docs/_static/orc_logo_grey.svg)
 ![ORC](https://raw.githubusercontent.com/localdevices/pyorc/main/docs/_static/orc_logo_bw.svg)

> **_Note_**: If you want to report misuse of an OpenRiverCam trademark, please contact us via [this](https://rainbowsensing.com/index.php/contact-2/) link.

## When do I need specific permission to use an OpenRiverCam trademark?

You may do the following without receiving specific permission from OpenRiverCam (or its affiliates):

 * Use OpenRiverCam wordmarks in text to truthfully refer to and/or link to unmodified OpenRiverCam programs, products, services and technologies.
 * Use OpenRiverCam logos in visuals to truthfully refer to and/or to link to the applicable programs, products, services and technologies hosted on OpenRiverCam servers.
 * Use OpenRiverCam wordmarks to explain that your software is based on OpenRiverCam's open source code, or is compatible with OpenRiverCam's software.
 * Describe a social media account, page, or community in accordance with the [Social Media Guidelines](#social-media-guidelines).

All other uses of an OpenRiverCam trademark require our prior written permission. This includes any use of a OpenRiverCam trademark in a domain name. Contact us at [this](https://rainbowsensing.com/index.php/contact-2/) link for more information.

## When allowed, how should I use an OpenRiverCam trademark?

### General Guidelines

#### Do:

 * Use the OpenRiverCam trademark exactly as shown in the list above.
 * Use OpenRiverCam wordmarks only as an adjective, never as a noun or verb. Do not use them in plural or possessive forms. Instead, use the generic term for the OpenRiverCam product or service following the trademark, for example: OpenRiverCam processing software.

#### Don't:

 * Don't use OpenRiverCam trademarks in any name or description of your business, product, service, app, domain name, publication, or other offering.
 * Don't use marks, logos, company names, slogans, domain names, or designs that are confusingly similar to OpenRiverCam trademarks.
 * Don't use OpenRiverCam trademarks in a way that incorrectly implies affiliation with, or sponsorship, endorsement, or approval by OpenRiverCam of your products or services.
 * Don't display OpenRiverCam trademarks more prominently than your product, service, or company name, when use is allowed.
 * Don't use OpenRiverCam trademarks on merchandise for sale (e.g., selling t-shirts, mugs, etc.)
 * Don't use OpenRiverCam trademarks for any other form of commercial use (e.g. offering technical support services), unless such use is limited to a truthful and descriptive reference (e.g. Independent technical support for OpenRiverCam's software).
 * Don't modify OpenRiverCam's trademarks, abbreviate them, or combine them with any other symbols, words, or images, or incorporate them into a tagline or slogan.

 ### Social Media Guidelines

In addition to the General Guidelines above, the name and handle of your social media account and any and all pages cannot begin with an OpenRiverCam trademark. In addition, OpenRiverCam logos cannot be used in a way that might suggest affiliation with OpenRiverCam, including, but not limited to, the account, profile, or header images. The only exception to these requirements is if you've received prior permission from OpenRiverCam affiliates.

For example, you cannot name your account, page, or community "OpenRiverCam Community" or "OpenRiverCam Software". However, it would be acceptable to name your account, page, or community "Fans of OpenRiverCam" or "Information about OpenRiverCam Software" as long as you do not use the OpenRiverCam trademarks or OpenRiverCam logos or otherwise suggest any affiliation with OpenRiverCam.

### Open Source Project Guidelines

The specific license for each of OpenRiverCam's software products and code says what you can and cannot do with the code itself but does not give permission to use OpenRiverCam's trademarks. If you choose to build on or modify OpenRiverCam's open source code for your own project,

#### You Must:

 * Follow the terms of the Open Source License(s) for OpenRiverCam software products and code.
 * Choose branding, logos, and trademarks that denotes your own unique identity so as to clearly signal to users that there is no affiliation with or endorsement by OpenRiverCam.
 * Follow the General Guidelines, above.

#### You Must NOT:

* Use any OpenRiverCam trademark in connection with the user-facing name or branding of your project.
 * Use any OpenRiverCam trademark or any part of any OpenRiverCam trademark to incorrectly suggest or give the impression your software is actually published by, affiliated with, or endorsed by OpenRiverCam.

For example, please do not name your project, [Something]-pyOpenRiverCam, or OpenRiverCam-[Something]

#### You May:

 * State in words (not using logos or images) that your product "works with" or "is compatible" with certain OpenRiverCam software products, if that is true.
 * State in words (not using logos or images) that your project is based on OpenRiverCam open source technology, if that is true, as long as you also include a statement that your project is not officially associated with OpenRiverCam or its products.

For instance, you may state that your project:

"is proudly built from OpenRiverCam's open source software"

as long as you also include the statement equally prominently:

"[Brand Name] and [Product Name] are not officially associated with OpenRiverCam or its products."

### Cloud-based Services

If you offer a cloud-based service that provides remote access to OpenRiverCam software, it is important that you do so in a way that does not confuse users about who is offering the service. You must not use OpenRiverCam trademarks in the name of your product or service.

You may state in words (not using logos or images) that your product or service features or provides access to unaltered OpenRiverCam software, if this is true.

For example:

Acceptable: [Your Product Name] featuring OpenRiverCam's processing software

Incorrect: OpenRiverCam [Your Product Name]

### OpenRiverCam Community Guidelines

These OpenRiverCam Trademark Guidelines do not alter any previously granted permissions for use of OpenRiverCam Trademarks.
