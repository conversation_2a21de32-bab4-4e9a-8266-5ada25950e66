{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["## Obtain a discharge measurement over a cross section\n", "\n", "Now that we have a suitable velocimetry result, we can extract river flows from it over user-provided cross sections. These must be provided in the same vertical reference as the water level was measured in, and they must eityher be in the same coordinate reference system (crs) as the ground control point observations used in the `CameraConfig` object, or in a crs that can be readily transformed into the crs of the camera projection. Below we first read the results back in memory"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import xarray as xr\n", "import pandas as pd\n", "import pyorc\n", "import cartopy.crs as ccrs\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import Normalize"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["First we read the masked results from notebook 03 back into memory. We also load the first frame of our original video in rgb format.\n"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["ds = xr.open_dataset(\"ngwerere/ngwerere_masked.nc\")\n", "\n", "# also open the original video file\n", "video_file = \"ngwerere/ngwerere_20191103.mp4\"\n", "video = pyorc.Video(video_file, start_frame=0, end_frame=1)\n", "\n", "# borrow the camera config from the velocimetry results\n", "video.camera_config = ds.velocimetry.camera_config\n", "\n", "# get the frame as rgb\n", "da_rgb = video.get_frames(method=\"rgb\")\n"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["We need a cross section. We have stored two cross sections in comma-separated text files (.csv) along with the examples. Below we load these in memory and we extract the x, y and z values from it. These are all measured in the same vertical reference as the water level. Let's first investigate if the cross sections are correctly referenced, by plotting them in the camera configuration."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["cross_section = pd.read_csv(\"ngwerere/ngwerere_cross_section.csv\")\n", "x = cross_section[\"x\"]\n", "y = cross_section[\"y\"]\n", "z = cross_section[\"z\"]\n", "cross_section2 = pd.read_csv(\"ngwerere/ngwerere_cross_section_2.csv\")\n", "x2 = cross_section2[\"x\"]\n", "y2 = cross_section2[\"y\"]\n", "z2 = cross_section2[\"z\"]\n", "\n", "# let's have a look at the cross sections, the coordinates of the cross sections are in UTM 35S coordinates, \n", "# so we have to tell the axes that the coordinates need to be transformed from that crs into the crs of the axes.\n", "# we also make a very very small buffer of 0.00005 degrees around the area of interest, so that we can \n", "# clearly see the cross sections.\n", "ax = ds.velocimetry.camera_config.plot(tiles=\"GoogleTiles\", zoom_level=22, tiles_kwargs={\"style\": \"satellite\"}, buffer=0.00005)\n", "ax.plot(x, y, \"g--\", transform=ccrs.UTM(zone=35, southern_hemisphere=True), label=\"Cross section #1\")\n", "ax.plot(x2, y2, \"y--\", transform=ccrs.UTM(zone=35, southern_hemisphere=True), label=\"Cross section #2\")\n", "ax.legend()\n", "\n"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["The cross sections are clearly in the area of interest, this means we can sample velocities from them.\n", "We use the x, y and z values, to get a transect of velocity values from the velocimetry results. The z-values are only used later, to fill in any missing velocities with a logarithmic profile. The `wdw` parameter is used to take the median of the velocity in a larger window. `wdw=1` is the default and means that a surrounding window of 3x3 is used to sample from. `wdw=0` means that only the pixel directly underneath the bathymetry point is used. To account for short duration variability in FPS or natural occurring velocities, we apply a little bit of smoothing in time on the results with a rolling mean over 4 time steps as a setting."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["ds_points = ds.velocimetry.get_transect(x, y, z, crs=32735, rolling=4)\n", "ds_points2 = ds.velocimetry.get_transect(x2, y2, z2, crs=32735, rolling=4)\n", "ds_points"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["You can see that all coordinates and variables now only have a `quantile` and `points` dimension. The `point` dimension represents all bathymetry points. By default, quantiles [0.05, 0.25, 0.5, 0.75, 0.95] are derived, but this can also be modified with the `quantile` parameter. During the sampling, the bathymetry points are resampled to more or less match the resolution of the velocimetry results, in order to get a dense enough sampling. You can also impose a resampling distance using the `distance` parameter. If you would set this to 0.1, then a velocity will be sampled each 0.1 meters. Because our velocimetry grid has a 0.13 m resolution, the distance will by default be 0.13. The variables `v_eff_nofill` and `v_dir` are added, which is the effective velocity scalar, and its angle, perpendicular to the cross-section direction. `v_eff_nofill` at this stage may contain gaps because of unknown velocities in moments and locations where the mask methods did not provide satisfactory samples.\n", "\n", "Using `.get_q`, missing velocities will be filled with a logarithmic profile fit, and depth integrated velocities [m2/s] computed. We apply this on both cross sections and then check out the results."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["ds_points_q = ds_points.transect.get_q(fill_method=\"log_interp\")\n", "ds_points_q2 = ds_points2.transect.get_q(fill_method=\"log_interp\")\n", "ds_points_q"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Now we have variables `v_eff` with velocities (filled with zeros where no data was found, but log profiles are also possible), and `q` and `q_nofill` which hold the depth integrated velocities. During depth integration the default assumption is that the depth average velocities is 0.9 times the surface velocity. This can be controlled by the `v_corr` parameter. Below, we make a quick plot of the `q` results for both profiles."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["ax = plt.axes()\n", "ds_points_q[\"v_eff\"].isel(quantile=2).plot(ax=ax)\n", "ds_points_q2[\"v_eff\"].isel(quantile=2).plot(ax=ax)\n", "plt.grid()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["We can also plot the sampled surface velocities in combination with the velocity grid with bespoke plotting functions, giving intuitive graphics. We do this in the camera perspective below, similar to notebook 03."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# plot the rgb frame first. We use the \"camera\" mode to plot the camera perspective.\n", "norm = Normalize(vmin=0., vmax=0.6, clip=False)\n", "\n", "p = da_rgb[0].frames.plot(mode=\"camera\")\n", "\n", "# extract mean velocity and plot in camera projection\n", "ds.mean(dim=\"time\", keep_attrs=True).velocimetry.plot(\n", "    ax=p.axes,\n", "    mode=\"camera\",\n", "    # cmap=\"rainbow\",\n", "    # scale=200,\n", "    alpha=0.3,\n", "    norm=norm,\n", ")\n", "\n", "# plot velocimetry point results in camera projection\n", "ds_points_q.isel(quantile=2).transect.plot(\n", "    ax=p.axes,\n", "    mode=\"camera\",\n", "    cmap=\"rainbow\",\n", "    norm=norm,\n", "    width=2  # width is always a dimensionless number, 1 is a good default, 2 is larger, 0.5 is smaller\n", ")\n", "ds_points_q2.isel(quantile=2).transect.plot(\n", "    ax=p.axes,\n", "    mode=\"camera\",\n", "    cmap=\"rainbow\",\n", "    norm=norm,\n", "    width=2,\n", "    add_colorbar=True\n", ")\n", "\n", "# store figure in a JPEG\n", "p.axes.figure.savefig(\"ngwerere.jpg\", dpi=200)"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Of course we can also plot our results in a local projection and add a selected plotting style to it. Below this is shown with a streamplot as example."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# again plot the projected background\n", "from matplotlib.colors import Normalize\n", "norm = Normalize(vmin=0, vmax=0.6, clip=False)\n", "ds_mean = ds.mean(dim=\"time\", keep_attrs=True)\n", "p = da_rgb.frames.project()[0].frames.plot(mode=\"local\")\n", "\n", "# plot velocimetry point results in local projection\n", "ds_points_q.isel(quantile=2).transect.plot(\n", "    ax=p.axes,\n", "    mode=\"local\",\n", "    cmap=\"rainbow\",\n", "    width=2,\n", "    norm=norm,\n", "    add_colorbar=True,\n", ")\n", "\n", "ds_points_q2.isel(quantile=2).transect.plot(\n", "    ax=p.axes,\n", "    mode=\"local\",\n", "    cmap=\"rainbow\",\n", "    width=2,\n", "    norm=norm,\n", "    add_colorbar=True,\n", ")\n", "# to ensure streamp<PERSON> understands the directions correctly, all values must \n", "# be flipped upside down and up-down velocities become down-up velocities.\n", "ds_mean.velocimetry.plot.streamplot(\n", "    ax=p.axes,\n", "    mode=\"local\",\n", "    density=3.,\n", "    minlength=0.05,\n", "    linewidth_scale=2,\n", "    cmap=\"rainbow\",\n", "    norm=norm,\n", "    add_colorbar=True\n", ")"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Finally, we can extract discharge estimates from the cross section"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["ds_points_q.transect.get_river_flow()\n", "print(ds_points_q[\"river_flow\"])\n", "ds_points_q2.transect.get_river_flow()\n", "print(ds_points_q2[\"river_flow\"])\n"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["You can see that the different quantiles give very diverse values, but that even with this very shallow and difficult example, the flow estimates are quite close to each other. The bathymetry was recorded at only 10 cm accuracy and the site is far from uniform, and so not ideal for cross-sectional discharge observations. This can easily explain the differences between two sampled cross sections. \n", "\n", "The diversity in quantile values can be because of many reasons and should be considered highly conservative. The real uncertainty is likely to be smaller in case velocities are well sampled. In case many velocities are filled in with interpolation, the uncertainty may also be larger. Reasons for variability are:\n", "\n", "- the integration assumes that all velocity estimates for the same quantile, occur entirely correlated. This is naturally not the case in reality. Instead, water may vary slightly in its path from moment to moment and therefore the quantiles are a very conservative estimate. Especially during very low and shallow flows with lots of variability in direction and scalar values (such as in this example) this may lead to quite wide variability. \n", "- Frames-per-second of a video are not as constant as we would like them to be and many cameras (in particular cheap IP cameras) are not good at keeping track of the time evolution per frame. This can cause a lot of variability which has nothing to do with real uncertainty. `pyorc` does read frame-specific timings in case frame rates are not constant.\n", "- last but not least, remaining uncertainties in the PIV solution can cause variability.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}