#!/usr/bin/env python3
"""
调试索引越界错误的诊断脚本

这个脚本帮助诊断在img_to_ortho函数中出现的IndexError问题。
主要检查相机配置与实际图像尺寸的匹配情况。
"""

import numpy as np
import xarray as xr
from pyorc.api.cameraconfig import CameraConfig


def diagnose_camera_config_mismatch(da, cc, x, y, z):
    """
    诊断相机配置与图像数据的匹配情况
    
    Parameters
    ----------
    da : xr.DataArray
        图像数据数组
    cc : CameraConfig
        相机配置对象
    x, y : np.ndarray
        目标网格坐标
    z : float
        水位高度
    """
    print("=" * 60)
    print("相机配置与图像数据诊断报告")
    print("=" * 60)
    
    # 1. 检查图像数据信息
    print(f"\n1. 图像数据信息:")
    print(f"   - 数据数组形状: {da.shape}")
    print(f"   - 数据类型: {da.dtype}")
    if len(da.shape) >= 2:
        actual_height, actual_width = da.shape[-2:]
        actual_pixels = actual_height * actual_width
        print(f"   - 实际图像尺寸: {actual_width} x {actual_height}")
        print(f"   - 实际像素总数: {actual_pixels}")
    
    # 2. 检查相机配置信息
    print(f"\n2. 相机配置信息:")
    print(f"   - 配置的图像宽度: {cc.width}")
    print(f"   - 配置的图像高度: {cc.height}")
    expected_pixels = cc.width * cc.height
    print(f"   - 配置的像素总数: {expected_pixels}")
    
    # 3. 检查匹配情况
    print(f"\n3. 匹配情况检查:")
    if len(da.shape) >= 2:
        width_match = actual_width == cc.width
        height_match = actual_height == cc.height
        pixels_match = actual_pixels == expected_pixels
        
        print(f"   - 宽度匹配: {width_match} ({actual_width} vs {cc.width})")
        print(f"   - 高度匹配: {height_match} ({actual_height} vs {cc.height})")
        print(f"   - 像素总数匹配: {pixels_match} ({actual_pixels} vs {expected_pixels})")
        
        if not (width_match and height_match):
            print(f"\n   ⚠️  警告: 图像尺寸不匹配!")
            print(f"      这很可能是导致索引越界错误的原因。")
            print(f"      建议检查相机配置文件中的width和height设置。")
    
    # 4. 检查投影参数
    print(f"\n4. 投影参数:")
    print(f"   - 目标网格 x 范围: [{np.min(x):.2f}, {np.max(x):.2f}] (长度: {len(x)})")
    print(f"   - 目标网格 y 范围: [{np.min(y):.2f}, {np.max(y):.2f}] (长度: {len(y)})")
    print(f"   - 水位高度 z: {z}")
    print(f"   - 目标网格总像素: {len(x) * len(y)}")
    
    # 5. 尝试获取索引映射信息（如果可能的话）
    print(f"\n5. 索引映射测试:")
    try:
        print("   正在计算索引映射...")
        idx_img, idx_ortho = cc.map_idx_img_ortho(x, y, z)
        
        print(f"   - 映射索引数量: {len(idx_img)}")
        print(f"   - idx_img 范围: [{np.min(idx_img)}, {np.max(idx_img)}]")
        print(f"   - idx_ortho 范围: [{np.min(idx_ortho)}, {np.max(idx_ortho)}]")
        
        # 检查索引是否超出边界
        max_valid_idx = expected_pixels - 1
        out_of_bounds = idx_img > max_valid_idx
        if np.any(out_of_bounds):
            n_oob = np.sum(out_of_bounds)
            print(f"   ❌ 发现 {n_oob} 个超出边界的索引!")
            print(f"      最大有效索引应为: {max_valid_idx}")
            print(f"      超出边界的索引示例: {idx_img[out_of_bounds][:5]}")
        else:
            print(f"   ✅ 所有索引都在有效范围内")
            
    except Exception as e:
        print(f"   ❌ 索引映射计算失败: {e}")
    
    # 6. 建议
    print(f"\n6. 建议:")
    if len(da.shape) >= 2 and (actual_width != cc.width or actual_height != cc.height):
        print("   - 更新相机配置文件中的width和height参数以匹配实际图像尺寸")
        print(f"     建议设置: width={actual_width}, height={actual_height}")
    else:
        print("   - 图像尺寸匹配正常，问题可能出现在其他地方")
        print("   - 检查相机标定参数是否正确")
        print("   - 检查GCP（地面控制点）设置是否合理")
    
    print("=" * 60)


def test_with_sample_data():
    """使用示例数据进行测试"""
    print("创建测试数据...")
    
    # 创建示例图像数据
    test_img = np.random.rand(1609, 3078).astype(np.float32)
    da_test = xr.DataArray(test_img, dims=['y', 'x'])
    
    # 创建示例相机配置（这里需要根据实际情况调整）
    # 注意：这只是一个示例，实际使用时需要正确的相机配置
    print("注意：这是一个测试示例，请使用您的实际相机配置进行诊断")
    
    return da_test


if __name__ == "__main__":
    print("PyORC 索引错误诊断工具")
    print("请在您的代码中调用 diagnose_camera_config_mismatch() 函数")
    print("例如：")
    print("from tests.debug_index_error import diagnose_camera_config_mismatch")
    print("diagnose_camera_config_mismatch(your_da, your_cc, your_x, your_y, your_z)")
