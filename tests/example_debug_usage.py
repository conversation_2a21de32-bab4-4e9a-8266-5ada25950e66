#!/usr/bin/env python3
"""
示例：如何在您的代码中使用调试工具

这个脚本展示了如何在遇到IndexError时使用调试工具来诊断问题。
"""

import numpy as np
import xarray as xr
from pyorc.api.cameraconfig import CameraConfig
from tests.debug_index_error import diagnose_camera_config_mismatch


def debug_your_pyorc_code():
    """
    在您的PyORC代码中添加调试的示例
    
    将这个函数的内容复制到您遇到问题的地方
    """
    
    # 假设您已经有了这些变量：
    # da - 您的图像数据
    # cc - 您的相机配置
    # x, y - 目标网格坐标
    # z - 水位高度
    
    print("开始调试PyORC索引错误...")
    
    # 1. 首先运行诊断工具
    print("\n步骤1: 运行诊断工具")
    try:
        # 替换为您的实际变量
        # diagnose_camera_config_mismatch(da, cc, x, y, z)
        print("请将 da, cc, x, y, z 替换为您的实际变量")
    except Exception as e:
        print(f"诊断过程中出错: {e}")
    
    # 2. 手动检查关键信息
    print("\n步骤2: 手动检查关键信息")
    
    # 检查图像数据
    # print(f"图像数据形状: {da.shape}")
    # print(f"图像数据类型: {da.dtype}")
    
    # 检查相机配置
    # print(f"相机配置宽度: {cc.width}")
    # print(f"相机配置高度: {cc.height}")
    # print(f"期望的像素总数: {cc.width * cc.height}")
    
    # 如果图像是2D的，检查实际尺寸
    # if len(da.shape) >= 2:
    #     actual_height, actual_width = da.shape[-2:]
    #     actual_pixels = actual_height * actual_width
    #     print(f"实际图像尺寸: {actual_width} x {actual_height}")
    #     print(f"实际像素总数: {actual_pixels}")
    #     
    #     # 检查是否匹配
    #     if actual_width != cc.width or actual_height != cc.height:
    #         print("❌ 发现尺寸不匹配!")
    #         print(f"建议更新相机配置: width={actual_width}, height={actual_height}")
    #     else:
    #         print("✅ 图像尺寸匹配")
    
    # 3. 尝试安全的投影（带错误处理）
    print("\n步骤3: 尝试安全投影")
    try:
        # 这里是您原来的投影代码，现在会有详细的调试信息
        # da_proj = da.frames.project(cc, x=x, y=y, z=z, reducer="mean")
        print("请在这里添加您的投影代码")
        print("现在代码会输出详细的调试信息")
    except IndexError as e:
        print(f"仍然遇到IndexError: {e}")
        print("请检查诊断工具的输出，并根据建议调整相机配置")
    except Exception as e:
        print(f"其他错误: {e}")


def fix_camera_config_example():
    """
    示例：如何修复相机配置
    """
    print("修复相机配置的示例:")
    
    # 假设您发现实际图像尺寸是 1609 x 3078
    # 而相机配置中设置的是其他值
    
    print("\n方法1: 直接修改CameraConfig对象")
    print("cc.width = 3078")
    print("cc.height = 1609")
    
    print("\n方法2: 创建新的相机配置")
    print("# 复制现有配置的其他参数")
    print("new_cc = CameraConfig(")
    print("    width=3078,")
    print("    height=1609,")
    print("    camera_matrix=cc.camera_matrix,")
    print("    dist_coeffs=cc.dist_coeffs,")
    print("    # ... 其他参数")
    print(")")
    
    print("\n方法3: 从配置文件重新加载并修改")
    print("# 编辑您的相机配置文件（JSON/YAML等）")
    print("# 更新其中的width和height字段")
    print("# 然后重新加载配置")


def common_solutions():
    """
    常见解决方案汇总
    """
    print("常见解决方案汇总:")
    print("=" * 50)
    
    print("\n1. 图像尺寸不匹配")
    print("   问题：相机配置的width/height与实际图像不符")
    print("   解决：更新相机配置参数")
    
    print("\n2. 图像预处理问题")
    print("   问题：图像在处理过程中被调整了尺寸")
    print("   解决：确保图像尺寸在整个流程中保持一致")
    
    print("\n3. 相机标定问题")
    print("   问题：相机矩阵或畸变系数不正确")
    print("   解决：重新进行相机标定")
    
    print("\n4. GCP设置问题")
    print("   问题：地面控制点设置不合理")
    print("   解决：检查并调整GCP坐标")
    
    print("\n5. 水位高度问题")
    print("   问题：z值设置不合理，导致投影超出图像范围")
    print("   解决：调整z值或检查参考水位设置")


if __name__ == "__main__":
    print("PyORC 调试工具使用示例")
    print("=" * 40)
    
    debug_your_pyorc_code()
    print("\n" + "=" * 40)
    
    fix_camera_config_example()
    print("\n" + "=" * 40)
    
    common_solutions()
    
    print("\n" + "=" * 40)
    print("使用说明:")
    print("1. 将此脚本中的示例代码复制到您的项目中")
    print("2. 替换示例变量为您的实际数据")
    print("3. 运行诊断工具获取详细信息")
    print("4. 根据诊断结果调整相机配置")
    print("5. 查看 .augument/docs/debug_index_error.md 获取更多信息")
